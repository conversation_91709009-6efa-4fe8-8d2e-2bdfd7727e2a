version : '3.8'
services:

  #网关
  yooa-gateway:
    container_name: yooa-gateway
    image: yooa-gateway:v3
    build:
      context: ./gateway
      dockerfile: dockerfile
    ports:
      - "18080:18080"
    environment:
      - TZ=Asia/Shanghai
    volumes:
    - ./logs/gateway:/home/<USER>/logs/yooa-gateway
    
    #鉴权
  yooa-auth:
    container_name: yooa-auth
    image: yooa-auth:v1
    build:
      context: ./auth
      dockerfile: dockerfile
    ports:
      - "19200:19200"
    environment:
      - TZ=Asia/Shanghai
    volumes:
    - ./logs/auth:/home/<USER>/logs/yooa-auth

  #业务模块 - 系统服务
  yooa-system:
    container_name: yooa-system
    image: yooa-system:v1
    build:
      context: ./system
      dockerfile: dockerfile
    ports:
      - "19201:19201"
    environment:
      - TZ=Asia/Shanghai
    volumes:
    - ./logs/system:/home/<USER>/logs/yooa-system
      
  #业务模块 - PD服务
  yooa-cmf:
    container_name: yooa-cmf
    image: yooa-cmf:v1
    build:
      context: ./cmf
      dockerfile: dockerfile
    ports:
      - "19202:19202"
    environment:
      - TZ=Asia/Shanghai
    volumes:
    - ./logs/cmf:/home/<USER>/logs/yooa-cmf
      
  #业务模块 - 推广服务
  yooa-extend:
    container_name: yooa-extend
    image: yooa-extend:v1
    build:
      context: ./extend
      dockerfile: dockerfile
    ports:
      - "19203:19203"
    environment:
      - TZ=Asia/Shanghai
    volumes:
    - ./logs/extend:/home/<USER>/logs/yooa-extend
      
  #业务模块 - 客户服务
  yooa-crm:
    container_name: yooa-crm
    image: yooa-crm:v1
    build:
      context: ./crm
      dockerfile: dockerfile
    ports:
      - "19204:19204"
    environment:
      - TZ=Asia/Shanghai
    volumes:
    - ./logs/crm:/home/<USER>/logs/yooa-crm
      
  #业务模块 - 定时任务
  yooa-job:
    container_name: yooa-job
    image: yooa-job:v1
    build:
      context: ./job
      dockerfile: dockerfile
    ports:
      - "19205:19205"
    environment:
      - TZ=Asia/Shanghai
    volumes:
    - ./logs/job:/home/<USER>/logs/yooa-job

  #业务模块 - 文件服务
  yooa-file:
    container_name: yooa-file
    image: yooa-file:v1
    build:
      context: ./file
      dockerfile: dockerfile
    ports:
      - "19300:19300"
    environment:
      - TZ=Asia/Shanghai
    volumes:
    - ./logs/file:/home/<USER>/logs/yooa-file
    
  #业务模块 - 自定义流程服务
  yooa-antflow:
    container_name: yooa-antflow
    image: yooa-antflow:v4
    build:
      context: ./antflow
      dockerfile: dockerfile
    ports:
      - "7001:7001"
    environment:
      - TZ=Asia/Shanghai
    volumes:
    - ./logs/antflow:/home/<USER>/logs/yooa-antflow


  #监控模块 - 监控
  # yooa-monitor:
    # container_name: yooa-monitor
    # image: yooa-monitor:v1
    # build:
      # context: ./monitor
      # dockerfile: dockerfile
    # ports:
      # - "19100:19100"
    # environment:
      # - TZ=Asia/Shanghai
   # volumes:
   # - ./logs/monitor:/home/<USER>/logs/yooa-monitor