worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;


	
	server {
        # listen       80;
        server_name  oa.yyouou.com;

		# location ^~/dist {                     
			# proxy_pass http://oa.yyouou.com;
			# root      /opt/nginx/html/vue/dist;
		# }

		location / {
			root   /home/<USER>/projects/old-oa;
			try_files $uri $uri/ /index.html;
			index  index.html index.htm;
		}
    }


    server {
        # listen       80;
        server_name  oatwo.yyouou.com;

        location / {
            root   /home/<USER>/projects/new-oa;
            try_files $uri $uri/ /index.html;
            index  index.html index.htm;
        }

        location /prod-api/{
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://************:18080/;
        }

        # 避免actuator暴露
        if ($request_uri ~ "/actuator") {
            return 403;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}