<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yooa</groupId>
    <artifactId>yooa</artifactId>
    <name>yooa</name>
    <version>3.6.4</version>
    <url/>

    <properties>
        <yooa.version>3.6.4</yooa.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
        <spring-boot.version>2.7.18</spring-boot.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.6.0</spring-cloud-alibaba.version>
        <spring-framework.version>5.3.33</spring-framework.version>
        <spring-boot-admin.version>2.7.15</spring-boot-admin.version>
        <tobato.version>1.27.2</tobato.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>2.0.0</pagehelper.boot.version>
        <druid.version>1.2.20</druid.version>
        <dynamic-ds.version>4.2.0</dynamic-ds.version>
        <mybatis-plus.version>3.5.3.2</mybatis-plus.version>
        <commons.io.version>2.13.0</commons.io.version>
        <velocity.version>2.3</velocity.version>
        <fastjson.version>2.0.43</fastjson.version>
        <jjwt.version>0.9.1</jjwt.version>
        <minio.version>8.2.2</minio.version>
        <qiniu.version>[7.13.0, 7.13.99]</qiniu.version>
        <poi.version>4.1.2</poi.version>
        <poi-tl.version>1.10.6</poi-tl.version>
        <lombok.version>1.18.30</lombok.version>
        <hutool.version>5.8.27</hutool.version>
        <transmittable-thread-local.version>2.14.4</transmittable-thread-local.version>
        <redisson.version>3.45.1</redisson.version>
        <easypoi.version>4.3.0</easypoi.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringFramework的依赖配置-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring-framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- FastDFS 分布式文件系统 -->
            <dependency>
                <groupId>com.github.tobato</groupId>
                <artifactId>fastdfs-client</artifactId>
                <version>${tobato.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.deepoove</groupId>
                <artifactId>poi-tl</artifactId>
                <version>${poi-tl.version}</version>
            </dependency>

            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- JSON 解析器和生成器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <!--hutool bom 工具类-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 核心模块 -->
            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-common-core</artifactId>
                <version>${yooa.version}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-common-security</artifactId>
                <version>${yooa.version}</version>
            </dependency>

            <!-- 数据脱敏 -->
            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-common-sensitive</artifactId>
                <version>${yooa.version}</version>
            </dependency>

            <!-- 权限范围 -->
            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-common-datascope</artifactId>
                <version>${yooa.version}</version>
            </dependency>

            <!-- 多数据源 -->
            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-common-datasource</artifactId>
                <version>${yooa.version}</version>
            </dependency>

            <!-- 分布式事务 -->
            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-common-seata</artifactId>
                <version>${yooa.version}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-common-log</artifactId>
                <version>${yooa.version}</version>
            </dependency>

            <!-- mybatis 扩展 -->
            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-common-mybatis</artifactId>
                <version>${yooa.version}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-common-redis</artifactId>
                <version>${yooa.version}</version>
            </dependency>

            <!-- kafka服务 -->
            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-common-kafka</artifactId>
                <version>${yooa.version}</version>
            </dependency>

            <!-- 幂等验证 -->
            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-common-idempotent</artifactId>
                <version>${yooa.version}</version>
            </dependency>

            <!-- API -->
            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-api-system</artifactId>
                <version>${yooa.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-api-cmf</artifactId>
                <version>${yooa.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-api-crm</artifactId>
                <version>${yooa.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-api-extend</artifactId>
                <version>${yooa.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-api-external</artifactId>
                <version>${yooa.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yooa</groupId>
                <artifactId>yooa-api-antflow</artifactId>
                <version>${yooa.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-spring-boot-starter</artifactId>
                <version>${easypoi.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>yooa-auth</module>
        <module>yooa-gateway</module>
        <module>yooa-modules</module>
        <module>yooa-api</module>
        <module>yooa-common</module>
    </modules>
    <packaging>pom</packaging>

    <!-- 以下依赖 全局所有的模块都会引入 -->
    <dependencies>
        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <nacos.username>nacos</nacos.username>
                <nacos.password>fBqntagOV4</nacos.password>
                <nacos.server-addr>192.168.50.59:18848</nacos.server-addr>
<!--                <nacos.namespace>fadb91b2-16d9-4679-9fee-f813e14edb73</nacos.namespace>-->
                <nacos.namespace>9b9b94aa-6831-4cbf-aa4c-dda4e51872b2</nacos.namespace>
                <!--                <nacos.namespace>7999ec65-a48d-48d9-8a2a-ce04e45c366f</nacos.namespace>-->
                <nacos.file-extension>yml</nacos.file-extension>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>test</profiles.active>
                <nacos.username>nacos</nacos.username>
                <nacos.password>fBqntagOV4</nacos.password>
                <nacos.server-addr>47.112.183.215:18848</nacos.server-addr>
                <nacos.namespace>5c850abd-fa1f-4252-8aea-3a7f1ee5c512</nacos.namespace>
                <nacos.file-extension>yml</nacos.file-extension>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>prod</profiles.active>
                <nacos.username>nacos</nacos.username>
                <nacos.password>fBqntagOV4</nacos.password>
                <nacos.server-addr>120.24.185.250:18848</nacos.server-addr>
                <nacos.namespace>959a11aa-ea59-4387-931e-4723ccc7018e</nacos.namespace>
                <nacos.file-extension>yml</nacos.file-extension>
            </properties>
        </profile>
    </profiles>
</project>
