server:
  port: 19505

spring:
  application:
    name: @artifactId@
  profiles:
    active: @profiles.active@
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: @nacos.server-addr@
        namespace: @nacos.namespace@
      config:
        server-addr: @nacos.server-addr@
        namespace: @nacos.namespace@
        file-extension: @nacos.file-extension@
        shared-configs:
          - <EMAIL>@.@nacos.file-extension@