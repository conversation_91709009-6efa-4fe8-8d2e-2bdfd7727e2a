<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.yooa</groupId>
        <artifactId>yooa-modules</artifactId>
        <version>3.6.4</version>
    </parent>

    <artifactId>yooa-system</artifactId>
    <packaging>jar</packaging>
    <description>系统服务</description>

    <dependencies>
        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- SpringBoot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!-- YoOA Common DataSource -->
        <dependency>
            <groupId>com.yooa</groupId>
            <artifactId>yooa-common-datasource</artifactId>
        </dependency>

        <!-- YoOA Common DataScope -->
        <dependency>
            <groupId>com.yooa</groupId>
            <artifactId>yooa-common-datascope</artifactId>
        </dependency>

        <!-- YoOA Common Log -->
        <dependency>
            <groupId>com.yooa</groupId>
            <artifactId>yooa-common-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yooa</groupId>
            <artifactId>yooa-api-antflow</artifactId>
            <version>3.6.4</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.yooa</groupId>
            <artifactId>yooa-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yooa</groupId>
            <artifactId>yooa-common-kafka</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/*.doc</exclude>
                    <exclude>**/*.docx</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.doc</include>
                    <include>**/*.docx</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>
