<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>io.github.mrtylerzhou</groupId>
    <artifactId>antflow-spring-boot-starter</artifactId>
    <version>${antflow.version}</version>
    <properties>

        <!-- 主版本        -->
        <antflow.version>0.84.0</antflow.version>

        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-boot.version>2.7.17</spring-boot.version>

        <!--  ORM      -->
        <mysql.version>8.0.27</mysql.version>
        <druid.version>1.1.17</druid.version>
        <mybatis-spring-start.version>2.2.0</mybatis-spring-start.version>
        <mybatis-plus.version>3.5.1</mybatis-plus.version>

        <!--  第三方包       -->
        <jgroups.version>4.2.30.Final</jgroups.version>
        <pinyin4j.version>2.5.0</pinyin4j.version>
        <fastjson2.version>2.0.53</fastjson2.version>
        <drools.version>6.5.0.Final</drools.version>
        <jodd-core.version>5.3.0</jodd-core.version>
        <joda-time.version>2.9.9</joda-time.version>
        <jodd-servlet.version>4.1.2</jodd-servlet.version>
        <jodd-mail.version>5.1.6</jodd-mail.version>
        <jodd-bean.version>4.1.2</jodd-bean.version>
        <jgraphx.version>4.2.2</jgraphx.version>
        <javax.enterprise.concurrent.version>1.0</javax.enterprise.concurrent.version>
        <transmittable-thread-local.version>2.12.6</transmittable-thread-local.version>
        <geronimo-jta.version>1.1.1</geronimo-jta.version>
        <java-uuid-generator.version>3.1.4</java-uuid-generator.version>
        <commons-io.version>2.6</commons-io.version>
        <guava.version>31.0.1-jre</guava.version>

    </properties>
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-autoconfigure</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter</artifactId>
    </dependency>
    <dependency>
        <groupId>io.github.mrtylerzhou</groupId>
        <artifactId>antflow-base</artifactId>
        <version>${antflow.version}</version>
    </dependency>
    <dependency>
        <groupId>io.github.mrtylerzhou</groupId>
        <artifactId>antflow-common</artifactId>
        <version>0.83.0</version>
    </dependency>
    <dependency>
        <groupId>io.github.mrtylerzhou</groupId>
        <artifactId>antflow-engine</artifactId>
        <version>${antflow.version}</version>
    </dependency>

    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <scope>provided</scope>
    </dependency>

    <dependency>
        <groupId>org.mybatis.spring.boot</groupId>
        <artifactId>mybatis-spring-boot-starter</artifactId>
        <version>${mybatis-spring-start.version}</version>
    </dependency>
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>${mybatis-plus.version}</version>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-configuration-processor</artifactId>
        <optional>true</optional>
    </dependency>
    <dependency>
        <groupId>org.jgroups</groupId>
        <artifactId>jgroups</artifactId>
        <version>${jgroups.version}</version>
    </dependency>

    <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid</artifactId>
        <version>${druid.version}</version>
    </dependency>
    <dependency>
        <groupId>com.belerweb</groupId>
        <artifactId>pinyin4j</artifactId>
        <version>${pinyin4j.version}</version>
    </dependency>

    <dependency>
        <groupId>com.alibaba.fastjson2</groupId>
        <artifactId>fastjson2</artifactId>
        <version>${fastjson2.version}</version>
    </dependency>
    <dependency>
        <groupId>com.alibaba.fastjson2</groupId>
        <artifactId>fastjson2-extension</artifactId>
        <version>${fastjson2.version}</version>
    </dependency>
    <dependency>
        <groupId>com.alibaba.fastjson2</groupId>
        <artifactId>fastjson2-extension-spring5</artifactId>
        <version>${fastjson2.version}</version>
    </dependency>
    <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-aspects</artifactId>
    </dependency>
    <dependency>
        <groupId>org.jodd</groupId>
        <artifactId>jodd-core</artifactId>
        <version>${jodd-core.version}</version>
    </dependency>
    <dependency>
        <groupId>joda-time</groupId>
        <artifactId>joda-time</artifactId>
        <version>${joda-time.version}</version>
    </dependency>
    <dependency>
        <groupId>org.jodd</groupId>
        <artifactId>jodd-servlet</artifactId>
        <version>${jodd-servlet.version}</version>
    </dependency>
    <dependency>
        <groupId>org.jodd</groupId>
        <artifactId>jodd-mail</artifactId>
        <version>${jodd-mail.version}</version>
    </dependency>
    <dependency>
        <groupId>org.jodd</groupId>
        <artifactId>jodd-bean</artifactId>
        <version>${jodd-bean.version}</version>
    </dependency>
    <dependency>
        <groupId>com.github.vlsi.mxgraph</groupId>
        <artifactId>jgraphx</artifactId>
        <version>${jgraphx.version}</version>
    </dependency>
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-extension</artifactId>
        <version>${mybatis-plus.version}</version>
    </dependency>
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>${mysql.version}</version>
    </dependency>
    <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>transmittable-thread-local</artifactId>
        <version>${transmittable-thread-local.version}</version>
    </dependency>
    <dependency>
        <groupId>javax.enterprise.concurrent</groupId>
        <artifactId>javax.enterprise.concurrent-api</artifactId>
        <version>${javax.enterprise.concurrent.version}</version>
    </dependency>
    <dependency>
        <groupId>org.drools</groupId>
        <artifactId>drools-core</artifactId>
        <version>${drools.version}</version>
    </dependency>
    <dependency>
        <groupId>org.drools</groupId>
        <artifactId>drools-compiler</artifactId>
        <version>${drools.version}</version>
    </dependency>
    <dependency>
        <groupId>org.drools</groupId>
        <artifactId>knowledge-api</artifactId>
        <version>${drools.version}</version>
    </dependency>
    <dependency>
        <groupId>org.apache.geronimo.specs</groupId>
        <artifactId>geronimo-jta_1.1_spec</artifactId>
        <version>${geronimo-jta.version}</version>
    </dependency>
    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
    </dependency>
    <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>${commons-io.version}</version>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.uuid</groupId>
        <artifactId>java-uuid-generator</artifactId>
        <version>${java-uuid-generator.version}</version>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
    </dependency>
    <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>javax.servlet-api</artifactId>
        <scope>provided</scope>
    </dependency>
   <!-- <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-webmvc</artifactId>

    </dependency>-->
    <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
    </dependency>
    <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpmime</artifactId>
    </dependency>
    <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
    </dependency>

</dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <repositories>
        <repository>
            <id>huaweicloud</id>
            <name>huaweicloud</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
        </repository>
    </repositories>
    <description>starter module of antflow</description>
    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>zypqqgc</id>
            <name>zypqqgc</name>
            <email><EMAIL></email>
            <roles>
                <role>Project Manager</role>
                <role>Architect</role>
            </roles>
        </developer>
    </developers>
    <scm>
        <connection>https://github.com/mrtylerzhou/AntFlow.git</connection>
        <developerConnection>scm:git:ssh://**************:mrtylerzhou/AntFlow.git</developerConnection>
        <url>https://github.com/mrtylerzhou/AntFlow</url>
    </scm>
    <url>https://github.com/mrtylerzhou/AntFlow</url>
    <build>
        <plugins>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>attach-empty-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                        <configuration>
                            <classifier>sources</classifier>
                            <includes>
                                <include>nonexistent/**</include>
                            </includes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--   central publish plugin    -->
            <plugin>
                <groupId>org.sonatype.central</groupId>
                <artifactId>central-publishing-maven-plugin</artifactId>
                <version>0.4.0</version>
                <extensions>true</extensions>
                <configuration>
                    <publishingServerId>zypqqgc</publishingServerId>
                    <tokenAuth>true</tokenAuth>
                </configuration>
            </plugin>
            <!--   source plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>none</phase>
                    </execution>
                </executions>
            </plugin>
            <!--   javadoc plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.9.1</version>
                <configuration>
                    <additionalJOptions>
                        <additionalJOption>-Xdoclint:none</additionalJOption>
                        <additionalparam>-quiet</additionalparam>
                    </additionalJOptions>
                    <reportOutputDirectory>${project.build.directory}/empty-javadoc</reportOutputDirectory>
                  <!--  <skip>true</skip>-->
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-gpg-plugin</artifactId>
                <version>1.5</version>
                <configuration>
                    <executable>d:\Program Files (x86)\GnuPG\bin\gpg.exe</executable>
                    <keyname>zypqqgc</keyname>
                </configuration>
                <executions>
                    <execution>
                        <id>sign-artifacts</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>sign</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>