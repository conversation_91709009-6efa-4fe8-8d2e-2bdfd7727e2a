<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmRoiMapper">

    <select id="selectPitcherRoiStatisticsGroupByExtend" resultType="com.yooa.crm.api.domain.vo.ExtendRoiVo">
        SELECT
            IFNULL(SUM(cost_data.pitcher_num), 0) AS pitcherNum,
            IFNULL(SUM(cost_data.money_cny), 0) AS moneyCny,
            IFNULL(SUM(cost_data.fb_click), 0) AS fbClick,
            IFNULL(SUM(cost_data.tk_friend), 0) AS tkFriend,
            IFNULL(SUM(cost_data.tk_effective_friend), 0) AS tkEffectiveFriend,
            IFNULL(SUM(roi_data.friend_num), 0) AS friendNum,
            IFNULL(SUM(roi_data.customer_num), 0) AS customerNum,
            IFNULL(SUM(roi_data.recharge_num), 0) AS rechargeNum,
            IFNULL(ROUND(SUM(roi_data.recharge_num) / SUM(roi_data.customer_num) * 100, 2), 0) AS rechargeRate,
            IFNULL(ROUND(SUM(roi_data.ltv_total) / SUM(roi_data.customer_num), 2), 0) AS arpu,
            IFNULL(ROUND(SUM(roi_data.ltv_total) / SUM(roi_data.recharge_num), 2), 0) AS arppu,
            IFNULL(ROUND(SUM(roi_data.ltv_total) - IFNULL(SUM(cost_data.money_cny), 0), 2), 0) AS profit,
            IFNULL(ROUND(SUM(roi_data.ltv_total) / IFNULL(SUM(cost_data.money_cny), 0) * 100, 2), 0) AS profitRate,
            IFNULL(SUM(roi_data.ltv_1),0) AS ltv1,
            IFNULL(SUM(roi_data.ltv_2),0) AS ltv2,
            IFNULL(SUM(roi_data.ltv_3),0) AS ltv3,
            IFNULL(SUM(roi_data.ltv_4),0) AS ltv4,
            IFNULL(SUM(roi_data.ltv_5),0) AS ltv5,
            IFNULL(SUM(roi_data.ltv_6),0) AS ltv6,
            IFNULL(SUM(roi_data.ltv_7),0) AS ltv7,
            IFNULL(SUM(roi_data.ltv_15),0) AS ltv15,
            IFNULL(SUM(roi_data.ltv_30),0) AS ltv30,
            IFNULL(SUM(roi_data.ltv_45),0) AS ltv45,
            IFNULL(SUM(roi_data.ltv_60),0) AS ltv60,
            IFNULL(SUM(roi_data.ltv_90),0) AS ltv90,
            IFNULL(SUM(roi_data.ltv_120),0) AS ltv120,
            IFNULL(SUM(roi_data.ltv_150),0) AS ltv150,
            IFNULL(SUM(roi_data.ltv_180),0) AS ltv180,
            IFNULL(SUM(roi_data.ltv_210),0) AS ltv210,
            IFNULL(SUM(roi_data.ltv_240),0) AS ltv240,
            IFNULL(SUM(roi_data.ltv_270),0) AS ltv270,
            IFNULL(SUM(roi_data.ltv_300),0) AS ltv300,
            IFNULL(SUM(roi_data.ltv_330),0) AS ltv330,
            IFNULL(SUM(roi_data.ltv_360),0) AS ltv360,
            IFNULL(SUM(roi_data.ltv_total),0) AS ltvTotal
        FROM
            (
                SELECT
                    su.extend_id AS extend_id,
                    su.extend_name AS extend_name,
                    su.extend_dept_name AS extend_dept_name,
                    su.extend_dept_names AS extend_dept_names,
                    IFNULL(SUM(base_data.friend_num),0) AS friend_num,
                    IFNULL(SUM(base_data.customer_num),0) AS customer_num,
                    SUM(CASE WHEN base_data.ltv_total != 0 THEN 1 ELSE 0 END) AS recharge_num,
                    IFNULL(SUM(base_data.ltv_1),0) AS ltv_1,
                    IFNULL(SUM(base_data.ltv_2),0) AS ltv_2,
                    IFNULL(SUM(base_data.ltv_3),0) AS ltv_3,
                    IFNULL(SUM(base_data.ltv_4),0) AS ltv_4,
                    IFNULL(SUM(base_data.ltv_5),0) AS ltv_5,
                    IFNULL(SUM(base_data.ltv_6),0) AS ltv_6,
                    IFNULL(SUM(base_data.ltv_7),0) AS ltv_7,
                    IFNULL(SUM(base_data.ltv_15),0) AS ltv_15,
                    IFNULL(SUM(base_data.ltv_30),0) AS ltv_30,
                    IFNULL(SUM(base_data.ltv_45),0) AS ltv_45,
                    IFNULL(SUM(base_data.ltv_60),0) AS ltv_60,
                    IFNULL(SUM(base_data.ltv_90),0) AS ltv_90,
                    IFNULL(SUM(base_data.ltv_120),0) AS ltv_120,
                    IFNULL(SUM(base_data.ltv_150),0) AS ltv_150,
                    IFNULL(SUM(base_data.ltv_180),0) AS ltv_180,
                    IFNULL(SUM(base_data.ltv_210),0) AS ltv_210,
                    IFNULL(SUM(base_data.ltv_240),0) AS ltv_240,
                    IFNULL(SUM(base_data.ltv_270),0) AS ltv_270,
                    IFNULL(SUM(base_data.ltv_300),0) AS ltv_300,
                    IFNULL(SUM(base_data.ltv_330),0) AS ltv_330,
                    IFNULL(SUM(base_data.ltv_360),0) AS ltv_360,
                    IFNULL(SUM(base_data.ltv_total),0) AS ltv_total
                FROM
                    (
                        SELECT
                            user_id AS extend_id,
                            nick_name AS extend_name,
                            dept_name AS extend_dept_name,
                            ancestors_names AS extend_dept_names
                        FROM yooa_system.sys_user u
                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                        <where>
                            AND d.dept_type = 1
                            <if test="query.extendName != null and query.extendName != ''">
                                AND u.nick_name like concat('%',#{query.extendName},'%')
                            </if>
                            <if test="query.extendDeptId != null">
                                AND (u.dept_id = #{query.extendDeptId} OR FIND_IN_SET(#{query.extendDeptId}, d.ancestors))
                            </if>
                        </where>
                    ) su
                LEFT JOIN
                    (
                        SELECT
                            f.friend_id AS friend_id,
                            f.extend_id AS extend_id,
                            COUNT(DISTINCT f.friend_id) AS friend_num,
                            COUNT(DISTINCT cf.friend_id) AS customer_num,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 0 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 0 DAY) THEN co.order_money ELSE 0 END) AS ltv_1,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 1 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 1 DAY) THEN co.order_money ELSE 0 END) AS ltv_2,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 2 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 2 DAY) THEN co.order_money ELSE 0 END) AS ltv_3,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 3 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 3 DAY) THEN co.order_money ELSE 0 END) AS ltv_4,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 4 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 4 DAY) THEN co.order_money ELSE 0 END) AS ltv_5,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 5 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 5 DAY) THEN co.order_money ELSE 0 END) AS ltv_6,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 6 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 6 DAY) THEN co.order_money ELSE 0 END) AS ltv_7,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 14 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 14 DAY) THEN co.order_money ELSE 0 END) AS ltv_15,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 29 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 29 DAY) THEN co.order_money ELSE 0 END) AS ltv_30,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 44 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 44 DAY) THEN co.order_money ELSE 0 END) AS ltv_45,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 59 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 59 DAY) THEN co.order_money ELSE 0 END) AS ltv_60,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 89 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 89 DAY) THEN co.order_money ELSE 0 END) AS ltv_90,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 119 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 119 DAY) THEN co.order_money ELSE 0 END) AS ltv_120,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 149 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 149 DAY) THEN co.order_money ELSE 0 END) AS ltv_150,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 179 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 179 DAY) THEN co.order_money ELSE 0 END) AS ltv_180,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 209 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 209 DAY) THEN co.order_money ELSE 0 END) AS ltv_210,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 239 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 239 DAY) THEN co.order_money ELSE 0 END) AS ltv_240,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 269 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 269 DAY) THEN co.order_money ELSE 0 END) AS ltv_270,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 299 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 299 DAY) THEN co.order_money ELSE 0 END) AS ltv_300,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 329 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 329 DAY) THEN co.order_money ELSE 0 END) AS ltv_330,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 359 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 359 DAY) THEN co.order_money ELSE 0 END) AS ltv_360,
                            SUM(CASE WHEN co.order_date >= f.record_date THEN co.order_money ELSE 0 END) AS ltv_total
                        FROM
                            (
                                SELECT friend_id,extend_id,record_date
                                FROM crm_friend
                                <where>
                                    <if test="query.mainChannelId != null">
                                        AND main_channel_id = #{query.mainChannelId}
                                    </if>
                                    <if test="query.subChannelId != null">
                                        AND sub_channel_id = #{query.subChannelId}
                                    </if>
                                    <if test="query.language != null and query.language != ''">
                                        AND language = #{query.language}
                                    </if>
                                    <if test="query.sex != null and query.sex != ''">
                                        AND sex = #{query.sex}
                                    </if>
                                    AND record_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                                    AND (type IS NULL OR type = 1 OR (type = 2 AND EXISTS (SELECT 1 FROM crm_customer_friend cf WHERE cf.friend_id = crm_friend.friend_id)))
                                    AND pitcher_id IN
                                    (
                                        SELECT user_id
                                        FROM yooa_system.sys_user u
                                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                                        <where>
                                            AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                                            <if test="query.pitcherId != null">
                                                AND u.user_id = #{query.pitcherId}
                                            </if>
                                            <if test="query.pitcherId == null">
                                                ${query.params.dataScope}
                                            </if>
                                        </where>
                                    )
                                </where>
                            ) f
                        LEFT JOIN
                            (
                                SELECT
                                    friend_id,
                                    customer_id,
                                    py_extend_id,
                                    DATE(begin_time) AS begin_date,
                                    DATE(end_time) AS end_date
                                FROM crm_customer_friend
                            ) cf ON f.friend_id = cf.friend_id
                        LEFT JOIN
                            (
                                SELECT
                                    customer_id,
                                    py_extend_id,
                                    order_money,
                                    DATE(order_time) AS order_date
                                FROM crm_customer_order
                            ) co ON co.customer_id = cf.customer_id AND co.py_extend_id = cf.py_extend_id
                                    AND co.order_date >= cf.begin_date AND (co.order_date &lt; cf.end_date OR cf.end_date IS NULL)
                        GROUP BY
                        f.friend_id,
                        f.extend_id
                    ) base_data ON su.extend_id = base_data.extend_id
                GROUP BY
                su.extend_id
            ) roi_data
        LEFT JOIN
            (
                SELECT
                    extend_id,
                    COUNT(DISTINCT cost_date) AS pitcher_num,
                    SUM(money_cny) AS money_cny,
                    SUM(fb_click) AS fb_click,
                    SUM(tk_friend) AS tk_friend,
                    SUM(tk_effective_friend) AS tk_effective_friend
                FROM yooa_crm.crm_cost
                <where>
                    AND cost_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                    AND pitcher_id IN
                    (
                    SELECT user_id
                    FROM yooa_system.sys_user u
                    LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                    <where>
                        AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                        <if test="query.pitcherId != null">
                            AND u.user_id = #{query.pitcherId}
                        </if>
                        <if test="query.pitcherId == null">
                            ${query.params.dataScope}
                        </if>
                    </where>
                    )
                    <if test="query.extendId != null">
                        AND extend_id = #{query.extendId}
                    </if>
                    <if test="query.mainChannelId != null">
                        AND main_channel_id = #{query.mainChannelId}
                    </if>
                    <if test="query.subChannelId != null">
                        AND sub_channel_id = #{query.subChannelId}
                    </if>
                    <if test="query.language != null and query.language != ''">
                        AND language = #{query.language}
                    </if>
                    <if test="query.sex != null and query.sex != ''">
                        AND sex = #{query.sex}
                    </if>
                </where>
                GROUP BY
                extend_id
            ) cost_data ON roi_data.extend_id = cost_data.extend_id
        WHERE roi_data.friend_num != 0
            OR (cost_data.money_cny != 0 AND cost_data.money_cny IS NOT NULL )
            OR (cost_data.fb_click != 0 AND cost_data.fb_click IS NOT NULL )
            OR (cost_data.tk_friend != 0 AND cost_data.tk_friend IS NOT NULL )
            OR (cost_data.tk_effective_friend != 0 AND cost_data.tk_effective_friend IS NOT NULL )
    </select>

    <select id="selectPitcherRoiGroupByExtend" resultType="com.yooa.crm.api.domain.vo.ExtendRoiVo">
        SELECT
            roi_data.*,
            IFNULL(cost_data.pitcher_num, 0) AS pitcherNum,
            IFNULL(cost_data.money_cny, 0) AS moneyCny,
            IFNULL(cost_data.fb_click, 0) AS fbClick,
            IFNULL(cost_data.tk_friend, 0) AS tkFriend,
            IFNULL(cost_data.tk_effective_friend, 0) AS tkEffectiveFriend,
            IFNULL(ROUND(roi_data.rechargeNum / roi_data.customerNum * 100, 2), 0) AS rechargeRate,
            IFNULL(ROUND(roi_data.ltvTotal / roi_data.customerNum, 2), 0) AS arpu,
            IFNULL(ROUND(roi_data.ltvTotal / roi_data.rechargeNum, 2), 0) AS arppu,
            IFNULL(ROUND(roi_data.ltvTotal - IFNULL(cost_data.money_cny, 0), 2), 0) AS profit,
            IFNULL(ROUND(roi_data.ltvTotal / IFNULL(cost_data.money_cny, 0) * 100, 2), 0) AS profitRate
        FROM
            (
                SELECT
                    su.extend_id AS extend_id,
                    su.extend_name AS extend_name,
                    su.extend_dept_name AS extend_dept_name,
                    su.extend_dept_names AS extend_dept_names,
                    IFNULL(SUM(base_data.friend_num),0) AS friendNum,
                    IFNULL(SUM(base_data.customer_num),0) AS customerNum,
                    SUM(CASE WHEN base_data.ltv_total != 0 THEN 1 ELSE 0 END) AS rechargeNum,
                    IFNULL(SUM(base_data.ltv_1),0) AS ltv_1,
                    IFNULL(SUM(base_data.ltv_2),0) AS ltv_2,
                    IFNULL(SUM(base_data.ltv_3),0) AS ltv_3,
                    IFNULL(SUM(base_data.ltv_4),0) AS ltv_4,
                    IFNULL(SUM(base_data.ltv_5),0) AS ltv_5,
                    IFNULL(SUM(base_data.ltv_6),0) AS ltv_6,
                    IFNULL(SUM(base_data.ltv_7),0) AS ltv_7,
                    IFNULL(SUM(base_data.ltv_15),0) AS ltv_15,
                    IFNULL(SUM(base_data.ltv_30),0) AS ltv_30,
                    IFNULL(SUM(base_data.ltv_45),0) AS ltv_45,
                    IFNULL(SUM(base_data.ltv_60),0) AS ltv_60,
                    IFNULL(SUM(base_data.ltv_90),0) AS ltv_90,
                    IFNULL(SUM(base_data.ltv_120),0) AS ltv_120,
                    IFNULL(SUM(base_data.ltv_150),0) AS ltv_150,
                    IFNULL(SUM(base_data.ltv_180),0) AS ltv_180,
                    IFNULL(SUM(base_data.ltv_210),0) AS ltv_210,
                    IFNULL(SUM(base_data.ltv_240),0) AS ltv_240,
                    IFNULL(SUM(base_data.ltv_270),0) AS ltv_270,
                    IFNULL(SUM(base_data.ltv_300),0) AS ltv_300,
                    IFNULL(SUM(base_data.ltv_330),0) AS ltv_330,
                    IFNULL(SUM(base_data.ltv_360),0) AS ltv_360,
                    IFNULL(SUM(base_data.ltv_total),0) AS ltvTotal
                FROM
                    (
                        SELECT
                            user_id AS extend_id,
                            nick_name AS extend_name,
                            dept_name AS extend_dept_name,
                            ancestors_names AS extend_dept_names
                        FROM yooa_system.sys_user u
                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                        <where>
                            AND d.dept_type = 1
                            <if test="query.extendName != null and query.extendName != ''">
                                AND u.nick_name like concat('%',#{query.extendName},'%')
                            </if>
                            <if test="query.extendDeptId != null">
                                AND (u.dept_id = #{query.extendDeptId} OR FIND_IN_SET(#{query.extendDeptId}, d.ancestors))
                            </if>
                        </where>
                    ) su
                LEFT JOIN
                    (
                        SELECT
                            f.friend_id AS friend_id,
                            f.extend_id AS extend_id,
                            COUNT(DISTINCT f.friend_id) AS friend_num,
                            COUNT(DISTINCT cf.friend_id) AS customer_num,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 0 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 0 DAY) THEN co.order_money ELSE 0 END) AS ltv_1,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 1 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 1 DAY) THEN co.order_money ELSE 0 END) AS ltv_2,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 2 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 2 DAY) THEN co.order_money ELSE 0 END) AS ltv_3,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 3 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 3 DAY) THEN co.order_money ELSE 0 END) AS ltv_4,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 4 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 4 DAY) THEN co.order_money ELSE 0 END) AS ltv_5,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 5 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 5 DAY) THEN co.order_money ELSE 0 END) AS ltv_6,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 6 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 6 DAY) THEN co.order_money ELSE 0 END) AS ltv_7,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 14 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 14 DAY) THEN co.order_money ELSE 0 END) AS ltv_15,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 29 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 29 DAY) THEN co.order_money ELSE 0 END) AS ltv_30,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 44 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 44 DAY) THEN co.order_money ELSE 0 END) AS ltv_45,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 59 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 59 DAY) THEN co.order_money ELSE 0 END) AS ltv_60,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 89 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 89 DAY) THEN co.order_money ELSE 0 END) AS ltv_90,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 119 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 119 DAY) THEN co.order_money ELSE 0 END) AS ltv_120,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 149 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 149 DAY) THEN co.order_money ELSE 0 END) AS ltv_150,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 179 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 179 DAY) THEN co.order_money ELSE 0 END) AS ltv_180,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 209 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 209 DAY) THEN co.order_money ELSE 0 END) AS ltv_210,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 239 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 239 DAY) THEN co.order_money ELSE 0 END) AS ltv_240,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 269 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 269 DAY) THEN co.order_money ELSE 0 END) AS ltv_270,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 299 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 299 DAY) THEN co.order_money ELSE 0 END) AS ltv_300,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 329 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 329 DAY) THEN co.order_money ELSE 0 END) AS ltv_330,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 359 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 359 DAY) THEN co.order_money ELSE 0 END) AS ltv_360,
                            SUM(CASE WHEN co.order_date >= f.record_date THEN co.order_money ELSE 0 END) AS ltv_total
                        FROM
                            (
                                SELECT
                                    friend_id,extend_id,record_date
                                FROM crm_friend
                                <where>
                                    <if test="query.mainChannelId != null">
                                        AND main_channel_id = #{query.mainChannelId}
                                    </if>
                                    <if test="query.subChannelId != null">
                                        AND sub_channel_id = #{query.subChannelId}
                                    </if>
                                    <if test="query.language != null and query.language != ''">
                                        AND language = #{query.language}
                                    </if>
                                    <if test="query.sex != null and query.sex != ''">
                                        AND sex = #{query.sex}
                                    </if>
                                    AND record_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                                    AND (type IS NULL OR type = 1 OR (type = 2 AND EXISTS (SELECT 1 FROM crm_customer_friend cf WHERE cf.friend_id = crm_friend.friend_id)))
                                    AND pitcher_id IN
                                    (
                                        SELECT user_id
                                        FROM yooa_system.sys_user u
                                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                                        <where>
                                            AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                                            <if test="query.pitcherId != null">
                                                AND u.user_id = #{query.pitcherId}
                                            </if>
                                            <if test="query.pitcherId == null">
                                                ${query.params.dataScope}
                                            </if>
                                        </where>
                                    )
                                </where>
                            ) f
                        LEFT JOIN
                            (
                                SELECT
                                    friend_id,
                                    customer_id,
                                    py_extend_id,
                                    DATE(begin_time) AS begin_date,
                                    DATE(end_time) AS end_date
                                FROM crm_customer_friend
                            ) cf ON f.friend_id = cf.friend_id
                        LEFT JOIN
                            (
                                SELECT
                                    customer_id,
                                    py_extend_id,
                                    order_money,
                                    DATE(order_time) AS order_date
                                FROM crm_customer_order
                            ) co ON co.customer_id = cf.customer_id AND co.py_extend_id = cf.py_extend_id
                                    AND co.order_date >= cf.begin_date AND (co.order_date &lt; cf.end_date OR cf.end_date IS NULL)
                        GROUP BY
                        f.friend_id,
                        f.extend_id
                    ) base_data ON su.extend_id = base_data.extend_id
                GROUP BY
                su.extend_id
            ) roi_data
        LEFT JOIN
            (
                SELECT
                    extend_id,
                    COUNT(DISTINCT cost_date) AS pitcher_num,
                    SUM(money_cny) AS money_cny,
                    SUM(fb_click) AS fb_click,
                    SUM(tk_friend) AS tk_friend,
                    SUM(tk_effective_friend) AS tk_effective_friend
                FROM yooa_crm.crm_cost
                <where>
                    AND cost_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                    AND pitcher_id IN
                    (
                    SELECT user_id
                    FROM yooa_system.sys_user u
                    LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                    <where>
                        AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                        <if test="query.pitcherId != null">
                            AND u.user_id = #{query.pitcherId}
                        </if>
                        <if test="query.pitcherId == null">
                            ${query.params.dataScope}
                        </if>
                    </where>
                    )
                    <if test="query.extendId != null">
                        AND extend_id = #{query.extendId}
                    </if>
                    <if test="query.mainChannelId != null">
                        AND main_channel_id = #{query.mainChannelId}
                    </if>
                    <if test="query.subChannelId != null">
                        AND sub_channel_id = #{query.subChannelId}
                    </if>
                    <if test="query.language != null and query.language != ''">
                        AND language = #{query.language}
                    </if>
                    <if test="query.sex != null and query.sex != ''">
                        AND sex = #{query.sex}
                    </if>
                </where>
                GROUP BY
                extend_id
            ) cost_data ON roi_data.extend_id = cost_data.extend_id
        WHERE roi_data.friendNum != 0
            OR (cost_data.money_cny != 0 AND cost_data.money_cny IS NOT NULL )
            OR (cost_data.fb_click != 0 AND cost_data.fb_click IS NOT NULL )
            OR (cost_data.tk_friend != 0 AND cost_data.tk_friend IS NOT NULL )
            OR (cost_data.tk_effective_friend != 0 AND cost_data.tk_effective_friend IS NOT NULL )
    </select>

    <select id="selectPitcherRoiStatisticsGroupByDate" resultType="com.yooa.crm.api.domain.vo.DateRoiVo">
        SELECT
            IFNULL(SUM(cost_data.pitcher_num), 0) AS pitcherNum,
            IFNULL(SUM(cost_data.money_cny), 0) AS moneyCny,
            IFNULL(SUM(cost_data.fb_click), 0) AS fbClick,
            IFNULL(SUM(cost_data.tk_friend), 0) AS tkFriend,
            IFNULL(SUM(cost_data.tk_effective_friend), 0) AS tkEffectiveFriend,
            IFNULL(SUM(roi_data.friend_num), 0) AS friendNum,
            IFNULL(SUM(roi_data.customer_num), 0) AS customerNum,
            IFNULL(SUM(roi_data.recharge_num), 0) AS rechargeNum,
            IFNULL(ROUND(SUM(roi_data.recharge_num) / SUM(roi_data.customer_num) * 100, 2), 0) AS rechargeRate,
            IFNULL(ROUND(SUM(roi_data.ltv_total) / SUM(roi_data.customer_num), 2), 0) AS arpu,
            IFNULL(ROUND(SUM(roi_data.ltv_total) / SUM(roi_data.recharge_num), 2), 0) AS arppu,
            IFNULL(ROUND(SUM(roi_data.ltv_total) - IFNULL(SUM(cost_data.money_cny), 0), 2), 0) AS profit,
            IFNULL(ROUND(SUM(roi_data.ltv_total) / IFNULL(SUM(cost_data.money_cny), 0) * 100, 2), 0) AS profitRate,
            IFNULL(SUM(roi_data.ltv_1),0) AS ltv1,
            IFNULL(SUM(roi_data.ltv_2),0) AS ltv2,
            IFNULL(SUM(roi_data.ltv_3),0) AS ltv3,
            IFNULL(SUM(roi_data.ltv_4),0) AS ltv4,
            IFNULL(SUM(roi_data.ltv_5),0) AS ltv5,
            IFNULL(SUM(roi_data.ltv_6),0) AS ltv6,
            IFNULL(SUM(roi_data.ltv_7),0) AS ltv7,
            IFNULL(SUM(roi_data.ltv_15),0) AS ltv15,
            IFNULL(SUM(roi_data.ltv_30),0) AS ltv30,
            IFNULL(SUM(roi_data.ltv_45),0) AS ltv45,
            IFNULL(SUM(roi_data.ltv_60),0) AS ltv60,
            IFNULL(SUM(roi_data.ltv_90),0) AS ltv90,
            IFNULL(SUM(roi_data.ltv_120),0) AS ltv120,
            IFNULL(SUM(roi_data.ltv_150),0) AS ltv150,
            IFNULL(SUM(roi_data.ltv_180),0) AS ltv180,
            IFNULL(SUM(roi_data.ltv_210),0) AS ltv210,
            IFNULL(SUM(roi_data.ltv_240),0) AS ltv240,
            IFNULL(SUM(roi_data.ltv_270),0) AS ltv270,
            IFNULL(SUM(roi_data.ltv_300),0) AS ltv300,
            IFNULL(SUM(roi_data.ltv_330),0) AS ltv330,
            IFNULL(SUM(roi_data.ltv_360),0) AS ltv360,
            IFNULL(SUM(roi_data.ltv_total),0) AS ltvTotal
        FROM
            (
                SELECT
                    sd.date,
                    IFNULL(SUM(base_data.friend_num),0) AS friend_num,
                    IFNULL(SUM(base_data.customer_num),0) AS customer_num,
                    SUM(CASE WHEN base_data.ltv_total != 0 THEN 1 ELSE 0 END) AS recharge_num,
                    IFNULL(SUM(base_data.ltv_1),0) AS ltv_1,
                    IFNULL(SUM(base_data.ltv_2),0) AS ltv_2,
                    IFNULL(SUM(base_data.ltv_3),0) AS ltv_3,
                    IFNULL(SUM(base_data.ltv_4),0) AS ltv_4,
                    IFNULL(SUM(base_data.ltv_5),0) AS ltv_5,
                    IFNULL(SUM(base_data.ltv_6),0) AS ltv_6,
                    IFNULL(SUM(base_data.ltv_7),0) AS ltv_7,
                    IFNULL(SUM(base_data.ltv_15),0) AS ltv_15,
                    IFNULL(SUM(base_data.ltv_30),0) AS ltv_30,
                    IFNULL(SUM(base_data.ltv_45),0) AS ltv_45,
                    IFNULL(SUM(base_data.ltv_60),0) AS ltv_60,
                    IFNULL(SUM(base_data.ltv_90),0) AS ltv_90,
                    IFNULL(SUM(base_data.ltv_120),0) AS ltv_120,
                    IFNULL(SUM(base_data.ltv_150),0) AS ltv_150,
                    IFNULL(SUM(base_data.ltv_180),0) AS ltv_180,
                    IFNULL(SUM(base_data.ltv_210),0) AS ltv_210,
                    IFNULL(SUM(base_data.ltv_240),0) AS ltv_240,
                    IFNULL(SUM(base_data.ltv_270),0) AS ltv_270,
                    IFNULL(SUM(base_data.ltv_300),0) AS ltv_300,
                    IFNULL(SUM(base_data.ltv_330),0) AS ltv_330,
                    IFNULL(SUM(base_data.ltv_360),0) AS ltv_360,
                    IFNULL(SUM(base_data.ltv_total),0) AS ltv_total
                FROM
                    (
                        SELECT date FROM yooa_system.sys_date WHERE date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                    ) sd
                LEFT JOIN
                    (
                        SELECT
                            f.friend_id,
                            f.record_date,
                            COUNT(DISTINCT f.friend_id) AS friend_num,
                            COUNT(DISTINCT cf.friend_id) AS customer_num,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 0 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 0 DAY) THEN co.order_money ELSE 0 END) AS ltv_1,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 1 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 1 DAY) THEN co.order_money ELSE 0 END) AS ltv_2,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 2 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 2 DAY) THEN co.order_money ELSE 0 END) AS ltv_3,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 3 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 3 DAY) THEN co.order_money ELSE 0 END) AS ltv_4,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 4 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 4 DAY) THEN co.order_money ELSE 0 END) AS ltv_5,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 5 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 5 DAY) THEN co.order_money ELSE 0 END) AS ltv_6,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 6 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 6 DAY) THEN co.order_money ELSE 0 END) AS ltv_7,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 14 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 14 DAY) THEN co.order_money ELSE 0 END) AS ltv_15,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 29 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 29 DAY) THEN co.order_money ELSE 0 END) AS ltv_30,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 44 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 44 DAY) THEN co.order_money ELSE 0 END) AS ltv_45,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 59 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 59 DAY) THEN co.order_money ELSE 0 END) AS ltv_60,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 89 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 89 DAY) THEN co.order_money ELSE 0 END) AS ltv_90,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 119 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 119 DAY) THEN co.order_money ELSE 0 END) AS ltv_120,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 149 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 149 DAY) THEN co.order_money ELSE 0 END) AS ltv_150,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 179 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 179 DAY) THEN co.order_money ELSE 0 END) AS ltv_180,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 209 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 209 DAY) THEN co.order_money ELSE 0 END) AS ltv_210,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 239 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 239 DAY) THEN co.order_money ELSE 0 END) AS ltv_240,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 269 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 269 DAY) THEN co.order_money ELSE 0 END) AS ltv_270,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 299 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 299 DAY) THEN co.order_money ELSE 0 END) AS ltv_300,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 329 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 329 DAY) THEN co.order_money ELSE 0 END) AS ltv_330,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 359 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 359 DAY) THEN co.order_money ELSE 0 END) AS ltv_360,
                            SUM(CASE WHEN co.order_date >= f.record_date THEN co.order_money ELSE 0 END) AS ltv_total
                        FROM
                            (
                                SELECT
                                    friend_id,
                                    record_date
                                FROM crm_friend
                                <where>
                                    <if test="query.mainChannelId != null">
                                        AND main_channel_id = #{query.mainChannelId}
                                    </if>
                                    <if test="query.subChannelId != null">
                                        AND sub_channel_id = #{query.subChannelId}
                                    </if>
                                    <if test="query.language != null and query.language != ''">
                                        AND language = #{query.language}
                                    </if>
                                    <if test="query.sex != null and query.sex != ''">
                                        AND sex = #{query.sex}
                                    </if>
                                    <if test="query.extendId != null">
                                        AND extend_id = #{query.extendId}
                                    </if>
                                    AND (record_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]})
                                    AND (type IS NULL OR type = 1 OR (type = 2 AND EXISTS (SELECT 1 FROM crm_customer_friend cf WHERE cf.friend_id = crm_friend.friend_id)))
                                    AND pitcher_id IN
                                    (
                                        SELECT user_id
                                        FROM yooa_system.sys_user u
                                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                                        <where>
                                            AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                                            <if test="query.pitcherId != null">
                                                AND u.user_id = #{query.pitcherId}
                                            </if>
                                            <if test="query.pitcherId == null">
                                                ${query.params.dataScope}
                                            </if>
                                        </where>
                                    )
                                </where>
                            ) f
                        LEFT JOIN
                            (
                                SELECT
                                    friend_id,
                                    customer_id,
                                    py_extend_id,
                                    DATE(begin_time) AS begin_date,
                                    DATE(end_time) AS end_date
                                FROM crm_customer_friend
                            ) cf ON f.friend_id = cf.friend_id
                        LEFT JOIN
                            (
                                SELECT
                                    customer_id,
                                    py_extend_id,
                                    order_money,
                                    DATE(order_time) AS order_date
                                FROM crm_customer_order
                            ) co ON co.customer_id = cf.customer_id AND co.py_extend_id = cf.py_extend_id
                                    AND co.order_date >= cf.begin_date AND (co.order_date &lt; cf.end_date OR cf.end_date IS NULL)
                        GROUP BY
                        f.friend_id,
                        f.record_date
                    ) base_data ON sd.date = base_data.record_date
                GROUP BY
                sd.date
            ) roi_data
        LEFT JOIN
            (
                SELECT
                    cost_date,
                    COUNT(DISTINCT extend_id) AS pitcher_num,
                    SUM(money_cny) AS money_cny,
                    SUM(fb_click) AS fb_click,
                    SUM(tk_friend) AS tk_friend,
                    SUM(tk_effective_friend) AS tk_effective_friend
                FROM yooa_crm.crm_cost
                <where>
                    AND cost_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                    AND pitcher_id IN (
                    SELECT user_id
                    FROM yooa_system.sys_user u
                    LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                    <where>
                        AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                        <if test="query.pitcherId != null">
                            AND u.user_id = #{query.pitcherId}
                        </if>
                        <if test="query.pitcherId == null">
                            ${query.params.dataScope}
                        </if>
                    </where>
                    )
                    <if test="query.mainChannelId != null">
                        AND main_channel_id = #{query.mainChannelId}
                    </if>
                    <if test="query.subChannelId != null">
                        AND sub_channel_id = #{query.subChannelId}
                    </if>
                    <if test="query.language != null and query.language != ''">
                        AND language = #{query.language}
                    </if>
                    <if test="query.sex != null and query.sex != ''">
                        AND sex = #{query.sex}
                    </if>
                    <if test="query.extendId != null">
                        AND extend_id = #{query.extendId}
                    </if>
                </where>
                GROUP BY
                cost_date
            ) cost_data ON roi_data.date = cost_data.cost_date
        WHERE roi_data.friend_num != 0
            OR (cost_data.money_cny != 0 AND cost_data.money_cny IS NOT NULL )
            OR (cost_data.fb_click != 0 AND cost_data.fb_click IS NOT NULL )
            OR (cost_data.tk_friend != 0 AND cost_data.tk_friend IS NOT NULL )
            OR (cost_data.tk_effective_friend != 0 AND cost_data.tk_effective_friend IS NOT NULL )
    </select>

    <select id="selectPitcherRoiGroupByDate" resultType="com.yooa.crm.api.domain.vo.DateRoiVo">
        SELECT
            roi_data.*,
            IFNULL(cost_data.pitcher_num, 0) AS pitcherNum,
            IFNULL(cost_data.money_cny, 0) AS moneyCny,
            IFNULL(cost_data.fb_click, 0) AS fbClick,
            IFNULL(cost_data.tk_friend, 0) AS tkFriend,
            IFNULL(cost_data.tk_effective_friend, 0) AS tkEffectiveFriend,
            IFNULL(ROUND(roi_data.rechargeNum / roi_data.customerNum * 100, 2), 0) AS rechargeRate,
            IFNULL(ROUND(roi_data.ltvTotal / roi_data.customerNum, 2), 0) AS arpu,
            IFNULL(ROUND(roi_data.ltvTotal / roi_data.rechargeNum, 2), 0) AS arppu,
            IFNULL(ROUND(roi_data.ltvTotal - IFNULL(cost_data.money_cny, 0), 2), 0) AS profit,
            IFNULL(ROUND(roi_data.ltvTotal / IFNULL(cost_data.money_cny, 0) * 100, 2), 0) AS profitRate
        FROM
            (
                SELECT
                    sd.date,
                    IFNULL(SUM(base_data.friend_num),0) AS friendNum,
                    IFNULL(SUM(base_data.customer_num),0) AS customerNum,
                    SUM(CASE WHEN base_data.ltv_total != 0 THEN 1 ELSE 0 END) AS rechargeNum,
                    IFNULL(SUM(base_data.ltv_1),0) AS ltv_1,
                    IFNULL(SUM(base_data.ltv_2),0) AS ltv_2,
                    IFNULL(SUM(base_data.ltv_3),0) AS ltv_3,
                    IFNULL(SUM(base_data.ltv_4),0) AS ltv_4,
                    IFNULL(SUM(base_data.ltv_5),0) AS ltv_5,
                    IFNULL(SUM(base_data.ltv_6),0) AS ltv_6,
                    IFNULL(SUM(base_data.ltv_7),0) AS ltv_7,
                    IFNULL(SUM(base_data.ltv_15),0) AS ltv_15,
                    IFNULL(SUM(base_data.ltv_30),0) AS ltv_30,
                    IFNULL(SUM(base_data.ltv_45),0) AS ltv_45,
                    IFNULL(SUM(base_data.ltv_60),0) AS ltv_60,
                    IFNULL(SUM(base_data.ltv_90),0) AS ltv_90,
                    IFNULL(SUM(base_data.ltv_120),0) AS ltv_120,
                    IFNULL(SUM(base_data.ltv_150),0) AS ltv_150,
                    IFNULL(SUM(base_data.ltv_180),0) AS ltv_180,
                    IFNULL(SUM(base_data.ltv_210),0) AS ltv_210,
                    IFNULL(SUM(base_data.ltv_240),0) AS ltv_240,
                    IFNULL(SUM(base_data.ltv_270),0) AS ltv_270,
                    IFNULL(SUM(base_data.ltv_300),0) AS ltv_300,
                    IFNULL(SUM(base_data.ltv_330),0) AS ltv_330,
                    IFNULL(SUM(base_data.ltv_360),0) AS ltv_360,
                    IFNULL(SUM(base_data.ltv_total),0) AS ltvTotal
                FROM
                    (
                        SELECT date FROM yooa_system.sys_date WHERE date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                    ) sd
                LEFT JOIN
                    (
                        SELECT
                            f.friend_id,
                            f.record_date,
                            COUNT(DISTINCT f.friend_id) AS friend_num,
                            COUNT(DISTINCT cf.friend_id) AS customer_num,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 0 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 0 DAY) THEN co.order_money ELSE 0 END) AS ltv_1,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 1 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 1 DAY) THEN co.order_money ELSE 0 END) AS ltv_2,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 2 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 2 DAY) THEN co.order_money ELSE 0 END) AS ltv_3,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 3 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 3 DAY) THEN co.order_money ELSE 0 END) AS ltv_4,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 4 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 4 DAY) THEN co.order_money ELSE 0 END) AS ltv_5,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 5 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 5 DAY) THEN co.order_money ELSE 0 END) AS ltv_6,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 6 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 6 DAY) THEN co.order_money ELSE 0 END) AS ltv_7,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 14 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 14 DAY) THEN co.order_money ELSE 0 END) AS ltv_15,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 29 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 29 DAY) THEN co.order_money ELSE 0 END) AS ltv_30,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 44 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 44 DAY) THEN co.order_money ELSE 0 END) AS ltv_45,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 59 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 59 DAY) THEN co.order_money ELSE 0 END) AS ltv_60,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 89 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 89 DAY) THEN co.order_money ELSE 0 END) AS ltv_90,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 119 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 119 DAY) THEN co.order_money ELSE 0 END) AS ltv_120,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 149 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 149 DAY) THEN co.order_money ELSE 0 END) AS ltv_150,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 179 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 179 DAY) THEN co.order_money ELSE 0 END) AS ltv_180,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 209 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 209 DAY) THEN co.order_money ELSE 0 END) AS ltv_210,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 239 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 239 DAY) THEN co.order_money ELSE 0 END) AS ltv_240,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 269 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 269 DAY) THEN co.order_money ELSE 0 END) AS ltv_270,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 299 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 299 DAY) THEN co.order_money ELSE 0 END) AS ltv_300,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 329 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 329 DAY) THEN co.order_money ELSE 0 END) AS ltv_330,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 359 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 359 DAY) THEN co.order_money ELSE 0 END) AS ltv_360,
                            SUM(CASE WHEN co.order_date >= f.record_date THEN co.order_money ELSE 0 END) AS ltv_total
                        FROM
                            (
                                SELECT
                                    friend_id,
                                    record_date
                                FROM crm_friend
                                <where>
                                    <if test="query.mainChannelId != null">
                                        AND main_channel_id = #{query.mainChannelId}
                                    </if>
                                    <if test="query.subChannelId != null">
                                        AND sub_channel_id = #{query.subChannelId}
                                    </if>
                                    <if test="query.language != null and query.language != ''">
                                        AND language = #{query.language}
                                    </if>
                                    <if test="query.sex != null and query.sex != ''">
                                        AND sex = #{query.sex}
                                    </if>
                                    <if test="query.extendId != null">
                                        AND extend_id = #{query.extendId}
                                    </if>
                                    AND (record_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]})
                                    AND (type IS NULL OR type = 1 OR (type = 2 AND EXISTS (SELECT 1 FROM crm_customer_friend cf WHERE cf.friend_id = crm_friend.friend_id)))
                                    AND pitcher_id IN
                                    (
                                        SELECT user_id
                                        FROM yooa_system.sys_user u
                                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                                        <where>
                                            AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                                            <if test="query.pitcherId != null">
                                                AND u.user_id = #{query.pitcherId}
                                            </if>
                                            <if test="query.pitcherId == null">
                                                ${query.params.dataScope}
                                            </if>
                                        </where>
                                    )
                                </where>
                            ) f
                        LEFT JOIN
                            (
                                SELECT
                                    friend_id,
                                    customer_id,
                                    py_extend_id,
                                    DATE(begin_time) AS begin_date,
                                    DATE(end_time) AS end_date
                                FROM crm_customer_friend
                            ) cf ON f.friend_id = cf.friend_id
                        LEFT JOIN
                            (
                                SELECT
                                    customer_id,
                                    py_extend_id,
                                    order_money,
                                    DATE(order_time) AS order_date
                                FROM crm_customer_order
                            ) co ON co.customer_id = cf.customer_id AND co.py_extend_id = cf.py_extend_id
                                    AND co.order_date >= cf.begin_date AND (co.order_date &lt; cf.end_date OR cf.end_date IS NULL)
                        GROUP BY
                        f.friend_id,
                        f.record_date
                    ) base_data ON sd.date = base_data.record_date
                GROUP BY
                sd.date
            ) roi_data
        LEFT JOIN
            (
                SELECT
                    cost_date,
                    COUNT(DISTINCT extend_id) AS pitcher_num,
                    SUM(money_cny) AS money_cny,
                    SUM(fb_click) AS fb_click,
                    SUM(tk_friend) AS tk_friend,
                    SUM(tk_effective_friend) AS tk_effective_friend
                FROM yooa_crm.crm_cost
                <where>
                    AND cost_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                    AND pitcher_id IN (
                        SELECT user_id
                        FROM yooa_system.sys_user u
                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                        <where>
                            AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                            <if test="query.pitcherId != null">
                                AND u.user_id = #{query.pitcherId}
                            </if>
                            <if test="query.pitcherId == null">
                                ${query.params.dataScope}
                            </if>
                        </where>
                    )
                    <if test="query.mainChannelId != null">
                        AND main_channel_id = #{query.mainChannelId}
                    </if>
                    <if test="query.subChannelId != null">
                        AND sub_channel_id = #{query.subChannelId}
                    </if>
                    <if test="query.language != null and query.language != ''">
                        AND language = #{query.language}
                    </if>
                    <if test="query.sex != null and query.sex != ''">
                        AND sex = #{query.sex}
                    </if>
                    <if test="query.extendId != null">
                        AND extend_id = #{query.extendId}
                    </if>
                </where>
                GROUP BY
                cost_date
            ) cost_data ON roi_data.date = cost_data.cost_date
        WHERE roi_data.friendNum != 0
            OR (cost_data.money_cny != 0 AND cost_data.money_cny IS NOT NULL )
            OR (cost_data.fb_click != 0 AND cost_data.fb_click IS NOT NULL )
            OR (cost_data.tk_friend != 0 AND cost_data.tk_friend IS NOT NULL )
            OR (cost_data.tk_effective_friend != 0 AND cost_data.tk_effective_friend IS NOT NULL )
    </select>

    <select id="selectPitcherRoiStatisticsGroupByPitcher" resultType="com.yooa.crm.api.domain.vo.PitcherRoiVo">
        SELECT
            IFNULL(SUM(cost_data.pitcher_num), 0) AS pitcherNum,
            IFNULL(SUM(cost_data.money_cny), 0) AS moneyCny,
            IFNULL(SUM(cost_data.fb_click), 0) AS fbClick,
            IFNULL(SUM(cost_data.tk_friend), 0) AS tkFriend,
            IFNULL(SUM(cost_data.tk_effective_friend), 0) AS tkEffectiveFriend,
            IFNULL(SUM(roi_data.friend_num), 0) AS friendNum,
            IFNULL(SUM(roi_data.customer_num), 0) AS customerNum,
            IFNULL(SUM(roi_data.recharge_num), 0) AS rechargeNum,
            IFNULL(ROUND(SUM(roi_data.recharge_num) / SUM(roi_data.customer_num) * 100, 2), 0) AS rechargeRate,
            IFNULL(ROUND(SUM(roi_data.ltv_total) / SUM(roi_data.customer_num), 2), 0) AS arpu,
            IFNULL(ROUND(SUM(roi_data.ltv_total) / SUM(roi_data.recharge_num), 2), 0) AS arppu,
            IFNULL(ROUND(SUM(roi_data.ltv_total) - IFNULL(SUM(cost_data.money_cny), 0), 2), 0) AS profit,
            IFNULL(ROUND(SUM(roi_data.ltv_total) / IFNULL(SUM(cost_data.money_cny), 0) * 100, 2), 0) AS profitRate,
            IFNULL(SUM(roi_data.ltv_1),0) AS ltv1,
            IFNULL(SUM(roi_data.ltv_2),0) AS ltv2,
            IFNULL(SUM(roi_data.ltv_3),0) AS ltv3,
            IFNULL(SUM(roi_data.ltv_4),0) AS ltv4,
            IFNULL(SUM(roi_data.ltv_5),0) AS ltv5,
            IFNULL(SUM(roi_data.ltv_6),0) AS ltv6,
            IFNULL(SUM(roi_data.ltv_7),0) AS ltv7,
            IFNULL(SUM(roi_data.ltv_15),0) AS ltv15,
            IFNULL(SUM(roi_data.ltv_30),0) AS ltv30,
            IFNULL(SUM(roi_data.ltv_45),0) AS ltv45,
            IFNULL(SUM(roi_data.ltv_60),0) AS ltv60,
            IFNULL(SUM(roi_data.ltv_90),0) AS ltv90,
            IFNULL(SUM(roi_data.ltv_120),0) AS ltv120,
            IFNULL(SUM(roi_data.ltv_150),0) AS ltv150,
            IFNULL(SUM(roi_data.ltv_180),0) AS ltv180,
            IFNULL(SUM(roi_data.ltv_210),0) AS ltv210,
            IFNULL(SUM(roi_data.ltv_240),0) AS ltv240,
            IFNULL(SUM(roi_data.ltv_270),0) AS ltv270,
            IFNULL(SUM(roi_data.ltv_300),0) AS ltv300,
            IFNULL(SUM(roi_data.ltv_330),0) AS ltv330,
            IFNULL(SUM(roi_data.ltv_360),0) AS ltv360,
            IFNULL(SUM(roi_data.ltv_total),0) AS ltvTotal
        FROM
            (
                SELECT
                    su.pitcher_id AS pitcher_id,
                    su.pitcher_name AS pitcher_name,
                    su.pitcher_dept_name AS pitcher_dept_name,
                    su.pitcher_dept_names AS pitcher_dept_names,
                    IFNULL(SUM(base_data.friend_num),0) AS friend_num,
                    IFNULL(SUM(base_data.customer_num),0) AS customer_num,
                    SUM(CASE WHEN base_data.ltv_total != 0 THEN 1 ELSE 0 END) AS recharge_num,
                    IFNULL(SUM(base_data.ltv_1),0) AS ltv_1,
                    IFNULL(SUM(base_data.ltv_2),0) AS ltv_2,
                    IFNULL(SUM(base_data.ltv_3),0) AS ltv_3,
                    IFNULL(SUM(base_data.ltv_4),0) AS ltv_4,
                    IFNULL(SUM(base_data.ltv_5),0) AS ltv_5,
                    IFNULL(SUM(base_data.ltv_6),0) AS ltv_6,
                    IFNULL(SUM(base_data.ltv_7),0) AS ltv_7,
                    IFNULL(SUM(base_data.ltv_15),0) AS ltv_15,
                    IFNULL(SUM(base_data.ltv_30),0) AS ltv_30,
                    IFNULL(SUM(base_data.ltv_45),0) AS ltv_45,
                    IFNULL(SUM(base_data.ltv_60),0) AS ltv_60,
                    IFNULL(SUM(base_data.ltv_90),0) AS ltv_90,
                    IFNULL(SUM(base_data.ltv_120),0) AS ltv_120,
                    IFNULL(SUM(base_data.ltv_150),0) AS ltv_150,
                    IFNULL(SUM(base_data.ltv_180),0) AS ltv_180,
                    IFNULL(SUM(base_data.ltv_210),0) AS ltv_210,
                    IFNULL(SUM(base_data.ltv_240),0) AS ltv_240,
                    IFNULL(SUM(base_data.ltv_270),0) AS ltv_270,
                    IFNULL(SUM(base_data.ltv_300),0) AS ltv_300,
                    IFNULL(SUM(base_data.ltv_330),0) AS ltv_330,
                    IFNULL(SUM(base_data.ltv_360),0) AS ltv_360,
                    IFNULL(SUM(base_data.ltv_total),0) AS ltv_total
                FROM
                    (
                        SELECT
                            user_id AS pitcher_id,
                            nick_name AS pitcher_name,
                            dept_name AS pitcher_dept_name,
                            ancestors_names AS pitcher_dept_names
                        FROM yooa_system.sys_user u
                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                        <where>
                            AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                            <if test="query.pitcherName != null and query.pitcherName != ''">
                                AND u.nick_name like concat('%',#{query.pitcherName},'%')
                            </if>
                            <if test="query.pitcherDeptId != null">
                                AND (u.dept_id = #{query.pitcherDeptId} OR FIND_IN_SET(#{query.pitcherDeptId}, d.ancestors))
                            </if>
                            <if test="query.pitcherId != null">
                                AND u.user_id = #{query.pitcherId}
                            </if>
                            <if test="query.pitcherId == null">
                                ${query.params.dataScope}
                            </if>
                        </where>
                    ) su
                LEFT JOIN
                    (
                        SELECT
                            f.friend_id AS friend_id,
                            f.pitcher_id AS pitcher_id,
                            COUNT(DISTINCT f.friend_id) AS friend_num,
                            COUNT(DISTINCT cf.friend_id) AS customer_num,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 0 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 0 DAY) THEN co.order_money ELSE 0 END) AS ltv_1,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 1 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 1 DAY) THEN co.order_money ELSE 0 END) AS ltv_2,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 2 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 2 DAY) THEN co.order_money ELSE 0 END) AS ltv_3,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 3 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 3 DAY) THEN co.order_money ELSE 0 END) AS ltv_4,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 4 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 4 DAY) THEN co.order_money ELSE 0 END) AS ltv_5,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 5 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 5 DAY) THEN co.order_money ELSE 0 END) AS ltv_6,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 6 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 6 DAY) THEN co.order_money ELSE 0 END) AS ltv_7,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 14 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 14 DAY) THEN co.order_money ELSE 0 END) AS ltv_15,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 29 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 29 DAY) THEN co.order_money ELSE 0 END) AS ltv_30,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 44 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 44 DAY) THEN co.order_money ELSE 0 END) AS ltv_45,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 59 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 59 DAY) THEN co.order_money ELSE 0 END) AS ltv_60,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 89 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 89 DAY) THEN co.order_money ELSE 0 END) AS ltv_90,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 119 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 119 DAY) THEN co.order_money ELSE 0 END) AS ltv_120,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 149 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 149 DAY) THEN co.order_money ELSE 0 END) AS ltv_150,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 179 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 179 DAY) THEN co.order_money ELSE 0 END) AS ltv_180,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 209 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 209 DAY) THEN co.order_money ELSE 0 END) AS ltv_210,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 239 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 239 DAY) THEN co.order_money ELSE 0 END) AS ltv_240,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 269 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 269 DAY) THEN co.order_money ELSE 0 END) AS ltv_270,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 299 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 299 DAY) THEN co.order_money ELSE 0 END) AS ltv_300,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 329 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 329 DAY) THEN co.order_money ELSE 0 END) AS ltv_330,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 359 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 359 DAY) THEN co.order_money ELSE 0 END) AS ltv_360,
                            SUM(CASE WHEN co.order_date >= f.record_date THEN co.order_money ELSE 0 END) AS ltv_total
                        FROM
                            (
                                SELECT
                                    friend_id,
                                    pitcher_id,
                                    record_date
                                FROM crm_friend
                                <where>
                                    <if test="query.mainChannelId != null">
                                        AND main_channel_id = #{query.mainChannelId}
                                    </if>
                                    <if test="query.subChannelId != null">
                                        AND sub_channel_id = #{query.subChannelId}
                                    </if>
                                    <if test="query.language != null and query.language != ''">
                                        AND language = #{query.language}
                                    </if>
                                    <if test="query.sex != null and query.sex != ''">
                                        AND sex = #{query.sex}
                                    </if>
                                    AND record_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                                    AND (type IS NULL OR type = 1 OR (type = 2 AND EXISTS (SELECT 1 FROM crm_customer_friend cf WHERE cf.friend_id = crm_friend.friend_id)))
                                    AND pitcher_id IN
                                    (
                                        SELECT user_id
                                        FROM yooa_system.sys_user u
                                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                                        <where>
                                            AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                                            <if test="query.pitcherId != null">
                                                AND u.user_id = #{query.pitcherId}
                                            </if>
                                            <if test="query.pitcherId == null">
                                                ${query.params.dataScope}
                                            </if>
                                        </where>
                                    )
                                </where>
                            ) f
                        LEFT JOIN
                            (
                                SELECT
                                    friend_id,
                                    customer_id,
                                    py_extend_id,
                                    DATE(begin_time) AS begin_date,
                                    DATE(end_time) AS end_date
                                FROM crm_customer_friend
                            ) cf ON f.friend_id = cf.friend_id
                        LEFT JOIN
                            (
                                SELECT
                                    customer_id,
                                    py_extend_id,
                                    order_money,
                                    DATE(order_time) AS order_date
                                FROM crm_customer_order
                            ) co ON co.customer_id = cf.customer_id AND co.py_extend_id = cf.py_extend_id
                                    AND co.order_date >= cf.begin_date AND (co.order_date &lt; cf.end_date OR cf.end_date IS NULL)
                        GROUP BY
                        f.friend_id,
                        f.pitcher_id
                    ) base_data ON su.pitcher_id = base_data.pitcher_id
                GROUP BY
                su.pitcher_id
            ) roi_data
        LEFT JOIN
            (
                SELECT
                    pitcher_id,
                    COUNT(DISTINCT extend_id,cost_date) AS pitcher_num,
                    SUM(money_cny) AS money_cny,
                    SUM(fb_click) AS fb_click,
                    SUM(tk_friend) AS tk_friend,
                    SUM(tk_effective_friend) AS tk_effective_friend
                FROM yooa_crm.crm_cost
                <where>
                    AND cost_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                    AND pitcher_id IN
                    (
                    SELECT user_id
                    FROM yooa_system.sys_user u
                    LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                    <where>
                        AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                        <if test="query.pitcherId != null">
                            AND u.user_id = #{query.pitcherId}
                        </if>
                        <if test="query.pitcherId == null">
                            ${query.params.dataScope}
                        </if>
                    </where>
                    )
                    <if test="query.mainChannelId != null">
                        AND main_channel_id = #{query.mainChannelId}
                    </if>
                    <if test="query.subChannelId != null">
                        AND sub_channel_id = #{query.subChannelId}
                    </if>
                    <if test="query.language != null and query.language != ''">
                        AND language = #{query.language}
                    </if>
                    <if test="query.sex != null and query.sex != ''">
                        AND sex = #{query.sex}
                    </if>
                </where>
                GROUP BY
                pitcher_id
            ) cost_data ON roi_data.pitcher_id = cost_data.pitcher_id
        WHERE roi_data.friend_num != 0
            OR (cost_data.money_cny != 0 AND cost_data.money_cny IS NOT NULL )
            OR (cost_data.fb_click != 0 AND cost_data.fb_click IS NOT NULL )
            OR (cost_data.tk_friend != 0 AND cost_data.tk_friend IS NOT NULL )
            OR (cost_data.tk_effective_friend != 0 AND cost_data.tk_effective_friend IS NOT NULL )
    </select>

    <select id="selectPitcherRoiGroupByPitcher" resultType="com.yooa.crm.api.domain.vo.PitcherRoiVo">
        SELECT
            roi_data.*,
            IFNULL(cost_data.pitcher_num, 0) AS pitcherNum,
            IFNULL(cost_data.money_cny, 0) AS moneyCny,
            IFNULL(cost_data.fb_click, 0) AS fbClick,
            IFNULL(cost_data.tk_friend, 0) AS tkFriend,
            IFNULL(cost_data.tk_effective_friend, 0) AS tkEffectiveFriend,
            IFNULL(ROUND(roi_data.rechargeNum / roi_data.customerNum * 100, 2), 0) AS rechargeRate,
            IFNULL(ROUND(roi_data.ltvTotal / roi_data.customerNum, 2), 0) AS arpu,
            IFNULL(ROUND(roi_data.ltvTotal / roi_data.rechargeNum, 2), 0) AS arppu,
            IFNULL(ROUND(roi_data.ltvTotal - IFNULL(cost_data.money_cny, 0), 2), 0) AS profit,
            IFNULL(ROUND(roi_data.ltvTotal / IFNULL(cost_data.money_cny, 0) * 100, 2), 0) AS profitRate
        FROM
            (
                SELECT
                    su.pitcher_id AS pitcher_id,
                    su.pitcher_name AS pitcher_name,
                    su.pitcher_dept_name AS pitcher_dept_name,
                    su.pitcher_dept_names AS pitcher_dept_names,
                    IFNULL(SUM(base_data.friend_num),0) AS friendNum,
                    IFNULL(SUM(base_data.customer_num),0) AS customerNum,
                    SUM(CASE WHEN base_data.ltv_total != 0 THEN 1 ELSE 0 END) AS rechargeNum,
                    IFNULL(SUM(base_data.ltv_1),0) AS ltv_1,
                    IFNULL(SUM(base_data.ltv_2),0) AS ltv_2,
                    IFNULL(SUM(base_data.ltv_3),0) AS ltv_3,
                    IFNULL(SUM(base_data.ltv_4),0) AS ltv_4,
                    IFNULL(SUM(base_data.ltv_5),0) AS ltv_5,
                    IFNULL(SUM(base_data.ltv_6),0) AS ltv_6,
                    IFNULL(SUM(base_data.ltv_7),0) AS ltv_7,
                    IFNULL(SUM(base_data.ltv_15),0) AS ltv_15,
                    IFNULL(SUM(base_data.ltv_30),0) AS ltv_30,
                    IFNULL(SUM(base_data.ltv_45),0) AS ltv_45,
                    IFNULL(SUM(base_data.ltv_60),0) AS ltv_60,
                    IFNULL(SUM(base_data.ltv_90),0) AS ltv_90,
                    IFNULL(SUM(base_data.ltv_120),0) AS ltv_120,
                    IFNULL(SUM(base_data.ltv_150),0) AS ltv_150,
                    IFNULL(SUM(base_data.ltv_180),0) AS ltv_180,
                    IFNULL(SUM(base_data.ltv_210),0) AS ltv_210,
                    IFNULL(SUM(base_data.ltv_240),0) AS ltv_240,
                    IFNULL(SUM(base_data.ltv_270),0) AS ltv_270,
                    IFNULL(SUM(base_data.ltv_300),0) AS ltv_300,
                    IFNULL(SUM(base_data.ltv_330),0) AS ltv_330,
                    IFNULL(SUM(base_data.ltv_360),0) AS ltv_360,
                    IFNULL(SUM(base_data.ltv_total),0) AS ltvTotal
                FROM
                    (
                        SELECT
                            user_id AS pitcher_id,
                            nick_name AS pitcher_name,
                            dept_name AS pitcher_dept_name,
                            ancestors_names AS pitcher_dept_names
                        FROM yooa_system.sys_user u
                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                        <where>
                            AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                            <if test="query.pitcherName != null and query.pitcherName != ''">
                                AND u.nick_name like concat('%',#{query.pitcherName},'%')
                            </if>
                            <if test="query.pitcherDeptId != null">
                                AND (u.dept_id = #{query.pitcherDeptId} OR FIND_IN_SET(#{query.pitcherDeptId}, d.ancestors))
                            </if>
                            <if test="query.pitcherId != null">
                                AND u.user_id = #{query.pitcherId}
                            </if>
                            <if test="query.pitcherId == null">
                                ${query.params.dataScope}
                            </if>
                        </where>
                    ) su
                LEFT JOIN
                    (
                        SELECT
                            f.friend_id AS friend_id,
                            f.pitcher_id AS pitcher_id,
                            COUNT(DISTINCT f.friend_id) AS friend_num,
                            COUNT(DISTINCT cf.friend_id) AS customer_num,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 0 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 0 DAY) THEN co.order_money ELSE 0 END) AS ltv_1,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 1 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 1 DAY) THEN co.order_money ELSE 0 END) AS ltv_2,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 2 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 2 DAY) THEN co.order_money ELSE 0 END) AS ltv_3,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 3 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 3 DAY) THEN co.order_money ELSE 0 END) AS ltv_4,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 4 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 4 DAY) THEN co.order_money ELSE 0 END) AS ltv_5,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 5 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 5 DAY) THEN co.order_money ELSE 0 END) AS ltv_6,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 6 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 6 DAY) THEN co.order_money ELSE 0 END) AS ltv_7,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 14 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 14 DAY) THEN co.order_money ELSE 0 END) AS ltv_15,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 29 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 29 DAY) THEN co.order_money ELSE 0 END) AS ltv_30,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 44 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 44 DAY) THEN co.order_money ELSE 0 END) AS ltv_45,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 59 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 59 DAY) THEN co.order_money ELSE 0 END) AS ltv_60,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 89 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 89 DAY) THEN co.order_money ELSE 0 END) AS ltv_90,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 119 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 119 DAY) THEN co.order_money ELSE 0 END) AS ltv_120,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 149 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 149 DAY) THEN co.order_money ELSE 0 END) AS ltv_150,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 179 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 179 DAY) THEN co.order_money ELSE 0 END) AS ltv_180,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 209 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 209 DAY) THEN co.order_money ELSE 0 END) AS ltv_210,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 239 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 239 DAY) THEN co.order_money ELSE 0 END) AS ltv_240,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 269 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 269 DAY) THEN co.order_money ELSE 0 END) AS ltv_270,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 299 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 299 DAY) THEN co.order_money ELSE 0 END) AS ltv_300,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 329 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 329 DAY) THEN co.order_money ELSE 0 END) AS ltv_330,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 359 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 359 DAY) THEN co.order_money ELSE 0 END) AS ltv_360,
                            SUM(CASE WHEN co.order_date >= f.record_date THEN co.order_money ELSE 0 END) AS ltv_total
                        FROM
                            (
                                SELECT
                                    friend_id,
                                    pitcher_id,
                                    record_date
                                FROM crm_friend
                                <where>
                                    <if test="query.mainChannelId != null">
                                        AND main_channel_id = #{query.mainChannelId}
                                    </if>
                                    <if test="query.subChannelId != null">
                                        AND sub_channel_id = #{query.subChannelId}
                                    </if>
                                    <if test="query.language != null and query.language != ''">
                                        AND language = #{query.language}
                                    </if>
                                    <if test="query.sex != null and query.sex != ''">
                                        AND sex = #{query.sex}
                                    </if>
                                    AND (type IS NULL OR type = 1 OR (type = 2 AND EXISTS (SELECT 1 FROM crm_customer_friend cf WHERE cf.friend_id = crm_friend.friend_id)))
                                    AND record_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                                    AND pitcher_id IN
                                    (
                                        SELECT user_id
                                        FROM yooa_system.sys_user u
                                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                                        <where>
                                            AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                                            <if test="query.pitcherId != null">
                                                AND u.user_id = #{query.pitcherId}
                                            </if>
                                            <if test="query.pitcherId == null">
                                                ${query.params.dataScope}
                                            </if>
                                        </where>
                                    )
                                </where>
                            ) f
                        LEFT JOIN
                            (
                                SELECT
                                    friend_id,
                                    customer_id,
                                    py_extend_id,
                                    DATE(begin_time) AS begin_date,
                                    DATE(end_time) AS end_date
                                FROM crm_customer_friend
                            ) cf ON f.friend_id = cf.friend_id
                        LEFT JOIN
                            (
                                SELECT
                                    customer_id,
                                    py_extend_id,
                                    order_money,
                                    DATE(order_time) AS order_date
                                FROM crm_customer_order
                            ) co ON co.customer_id = cf.customer_id AND co.py_extend_id = cf.py_extend_id
                                    AND co.order_date >= cf.begin_date AND (co.order_date &lt; cf.end_date OR cf.end_date IS NULL)
                        GROUP BY
                        f.friend_id,
                        f.pitcher_id
                    ) base_data ON su.pitcher_id = base_data.pitcher_id
                GROUP BY
                su.pitcher_id
            ) roi_data
        LEFT JOIN
            (
                SELECT
                    pitcher_id,
                    COUNT(DISTINCT extend_id,cost_date) AS pitcher_num,
                    SUM(money_cny) AS money_cny,
                    SUM(fb_click) AS fb_click,
                    SUM(tk_friend) AS tk_friend,
                    SUM(tk_effective_friend) AS tk_effective_friend
                FROM yooa_crm.crm_cost
                <where>
                    AND cost_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                    AND pitcher_id IN
                    (
                    SELECT user_id
                    FROM yooa_system.sys_user u
                    LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                    <where>
                        AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                        <if test="query.pitcherId != null">
                            AND u.user_id = #{query.pitcherId}
                        </if>
                        <if test="query.pitcherId == null">
                            ${query.params.dataScope}
                        </if>
                    </where>
                    )
                    <if test="query.mainChannelId != null">
                        AND main_channel_id = #{query.mainChannelId}
                    </if>
                    <if test="query.subChannelId != null">
                        AND sub_channel_id = #{query.subChannelId}
                    </if>
                    <if test="query.language != null and query.language != ''">
                        AND language = #{query.language}
                    </if>
                    <if test="query.sex != null and query.sex != ''">
                        AND sex = #{query.sex}
                    </if>
                </where>
                GROUP BY
                pitcher_id
            ) cost_data ON roi_data.pitcher_id = cost_data.pitcher_id
        WHERE roi_data.friendNum != 0
            OR (cost_data.money_cny != 0 AND cost_data.money_cny IS NOT NULL )
            OR (cost_data.fb_click != 0 AND cost_data.fb_click IS NOT NULL )
            OR (cost_data.tk_friend != 0 AND cost_data.tk_friend IS NOT NULL )
            OR (cost_data.tk_effective_friend != 0 AND cost_data.tk_effective_friend IS NOT NULL )
    </select>

    <select id="selectPitcherPerformanceStatistics" resultType="com.yooa.crm.api.domain.vo.PitcherPerformanceVo">
        WITH pitcher_user AS (
            SELECT
                user_id AS pitcher_id,
                nick_name AS pitcher_name,
                dept_name AS pitcher_dept_name,
                ancestors_names AS pitcher_dept_names
            FROM yooa_system.sys_user u
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
            <where>
                AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                <if test="query.pitcherName != null and query.pitcherName != ''">
                    AND u.nick_name like concat('%',#{query.pitcherName},'%')
                </if>
                <if test="query.pitcherDeptId != null">
                    AND (u.dept_id = #{query.pitcherDeptId} OR FIND_IN_SET(#{query.pitcherDeptId}, d.ancestors))
                </if>
                <if test="query.pitcherId != null">
                    AND u.user_id = #{query.pitcherId}
                </if>
                <if test="query.pitcherId == null">
                    ${query.params.dataScope}
                </if>
            </where>
        )

        SELECT
            IFNULL(SUM(cost.fb_click),0) AS fbClick,
            IFNULL(SUM(cost.tk_friend),0) AS tkFriend,
            IFNULL(SUM(cost.tk_effective_friend),0) AS tkEffectiveFriend,
            IFNULL(SUM(f.friend_num),0) AS friendNum,
            IFNULL(SUM(cf.customer_num),0) AS customerNum,
            IFNULL(SUM(cja.handover_anchor_num),0) AS handoverAnchorNum,
            IFNULL(SUM(cjs.handover_serve_num),0) AS handoverServeNum,
            IFNULL(SUM(ev.first_charge_num),0) AS firstChargeNum,
            IFNULL(SUM(co.order_money),0) AS orderMoney,
            IFNULL(SUM(coxz.order_money),0) AS orderXzMoney,
            IFNULL(SUM(qf.quality_num),0) AS qualityNum
        FROM
            pitcher_user pu
        LEFT JOIN
            (
                SELECT
                    f.pitcher_id,
                    COUNT(f.friend_id) AS friend_num
                FROM crm_friend f
                WHERE f.pitcher_id IS NOT NULL
                AND (type IS NULL OR type = 1 OR (type = 2 AND EXISTS (SELECT 1 FROM crm_customer_friend cf WHERE cf.friend_id = f.friend_id)))
                AND record_date >= #{query.dateRange[0]}
                AND record_date &lt;= #{query.dateRange[1]}
                GROUP BY pitcher_id
            ) f ON pu.pitcher_id = f.pitcher_id
        LEFT JOIN
            (
                SELECT
                    f.pitcher_id,
                    COUNT(DISTINCT f.friend_id) AS customer_num
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                                            AND DATE(cf.begin_time) >= #{query.dateRange[0]}
                                            AND DATE(cf.begin_time) &lt;= #{query.dateRange[1]}
                GROUP BY f.pitcher_id
            ) cf ON pu.pitcher_id = cf.pitcher_id
        LEFT JOIN
            (
                SELECT
                pitcher_id,
                COUNT(cja.friend_id) AS handover_anchor_num
                FROM
                    (
                        SELECT
                            f.pitcher_id,
                            f.friend_id,
                            cja.receive_time
                        FROM pitcher_user pu
                        JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                        JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                        JOIN crm_customer_join_anchor cja ON cja.customer_id = cf.customer_id
                                                        AND cja.extend_id = cf.py_extend_id
                                                        AND DATE(cja.receive_time) >= DATE(cf.begin_time)
                                                        AND (DATE(cja.receive_time) &lt; DATE(cf.end_time) OR cf.end_time IS NULL)
                                                        AND cja.status = '1'
                        GROUP BY f.pitcher_id,f.friend_id
                    ) cja
                WHERE DATE(cja.receive_time) >= #{query.dateRange[0]} AND DATE(cja.receive_time) &lt;= #{query.dateRange[1]}
                GROUP BY cja.pitcher_id
            ) cja ON pu.pitcher_id = cja.pitcher_id
        LEFT JOIN
            (
                SELECT
                    pitcher_id,
                    COUNT(cjs.friend_id) AS handover_serve_num
                FROM
                (
                    SELECT
                        f.pitcher_id,
                        f.friend_id,
                        cjs.receive_time
                    FROM pitcher_user pu
                    JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                    JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                    JOIN crm_customer_join_serve cjs ON cjs.customer_id = cf.customer_id
                                                    AND cjs.extend_id = cf.py_extend_id
                                                    AND DATE(cjs.receive_time) >= DATE(cf.begin_time)
                                                    AND (DATE(cjs.receive_time) &lt; DATE(cf.end_time) OR cf.end_time IS NULL)
                                                    AND cjs.status = '1'
                    GROUP BY f.pitcher_id,f.friend_id
                ) cjs
                WHERE DATE(cjs.receive_time) >= #{query.dateRange[0]} AND DATE(cjs.receive_time) &lt;= #{query.dateRange[1]}
                GROUP BY cjs.pitcher_id
            ) cjs ON pu.pitcher_id = cjs.pitcher_id
        LEFT JOIN
            (
                SELECT
                    f.pitcher_id,
                    count(DISTINCT f.friend_id) AS first_charge_num
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                JOIN yooa_extend.extend_vermicelli ev ON f.friend_id = ev.friend_id
                                                    AND ev.fans_type = 5
                                                    AND DATE(ev.record_date) >= #{query.dateRange[0]}
                                                    AND DATE(ev.record_date) &lt;= #{query.dateRange[1]}
                GROUP BY f.pitcher_id
            ) ev ON pu.pitcher_id = ev.pitcher_id
        LEFT JOIN
            (
                SELECT
                    f.pitcher_id,
                    SUM(order_money) AS order_money
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                JOIN crm_customer_order co ON cf.customer_id = co.customer_id
                                        AND cf.py_extend_id = co.py_extend_id
                                        AND DATE(co.order_time) >= DATE(cf.begin_time)
                                        AND (DATE(co.order_time) &lt; DATE(cf.end_time) OR cf.end_time IS NULL)
                                        AND DATE(co.order_time) >= #{query.dateRange[0]}
                                        AND DATE(co.order_time) &lt;= #{query.dateRange[1]}
                                        AND co.order_status = '1'
                GROUP BY f.pitcher_id
            ) co ON pu.pitcher_id = co.pitcher_id
        LEFT JOIN
        (
        SELECT
        f.pitcher_id,
        SUM(order_money) AS order_money
        FROM pitcher_user pu
        JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
        JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
        JOIN crm_customer_order co ON cf.customer_id = co.customer_id
        AND cf.py_extend_id = co.py_extend_id
        AND DATE(co.order_time) >= DATE(cf.begin_time)
        AND (DATE(co.order_time) &lt; DATE(cf.end_time) OR cf.end_time IS NULL)
        AND DATE(co.order_time) >= #{query.dateRange[0]}
        AND DATE(co.order_time) &lt;= #{query.dateRange[1]}
        AND co.order_status = '1'
        AND co.order_time >= DATE_SUB(NOW(), INTERVAL 45 DAY)
        GROUP BY f.pitcher_id
        ) coxz ON pu.pitcher_id = coxz.pitcher_id
        LEFT JOIN
            (
                SELECT
                    pitcher_id,
                    SUM(fb_click) AS fb_click,
                    SUM(tk_friend) AS tk_friend,
                    SUM(tk_effective_friend) AS tk_effective_friend
                FROM yooa_crm.crm_cost
                WHERE cost_date >= #{query.dateRange[0]} AND cost_date &lt;= #{query.dateRange[1]}
                GROUP BY pitcher_id
            ) cost ON pu.pitcher_id = cost.pitcher_id
        LEFT JOIN
            (
                SELECT
                    f.pitcher_id,
                    COUNT(DISTINCT f.friend_id) AS quality_num
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                                            AND cf.quality_time IS NOT NULL
                                            AND DATE(cf.quality_time) >= #{query.dateRange[0]}
                                            AND DATE(cf.quality_time) &lt;= #{query.dateRange[1]}
                GROUP BY f.pitcher_id
            ) qf ON pu.pitcher_id = qf.pitcher_id
    </select>

    <select id="selectPitcherPerformance" resultType="com.yooa.crm.api.domain.vo.PitcherPerformanceVo">
        WITH pitcher_user AS (
            SELECT
                user_id AS pitcher_id,
                nick_name AS pitcher_name,
                dept_name AS pitcher_dept_name,
                ancestors_names AS pitcher_dept_names
            FROM yooa_system.sys_user u
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
            <where>
                AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                <if test="query.pitcherName != null and query.pitcherName != ''">
                    AND u.nick_name like concat('%',#{query.pitcherName},'%')
                </if>
                <if test="query.pitcherDeptId != null">
                    AND (u.dept_id = #{query.pitcherDeptId} OR FIND_IN_SET(#{query.pitcherDeptId}, d.ancestors))
                </if>
                <if test="query.pitcherId != null">
                    AND u.user_id = #{query.pitcherId}
                </if>
                <if test="query.pitcherId == null">
                    ${query.params.dataScope}
                </if>
            </where>
        )

        SELECT
            pu.*,
            IFNULL(cost.fb_click,0) AS fbClick,
            IFNULL(cost.tk_friend,0) AS tkFriend,
            IFNULL(cost.tk_effective_friend,0) AS tkEffectiveFriend,
            IFNULL(f.friend_num,0) AS friendNum,
            IFNULL(cf.customer_num,0) AS customerNum,
            IFNULL(cja.handover_anchor_num,0) AS handoverAnchorNum,
            IFNULL(cjs.handover_serve_num,0) AS handoverServeNum,
            IFNULL(ev.first_charge_num,0) AS firstChargeNum,
            IFNULL(co.order_money,0) AS orderMoney,
            IFNULL(coxz.order_money,0) AS orderXzMoney,
            IFNULL(qf.quality_num,0) AS qualityNum
        FROM
            pitcher_user pu
        LEFT JOIN
            (
                SELECT
                    f.pitcher_id,
                    COUNT(f.friend_id) AS friend_num
                FROM crm_friend f
                WHERE f.pitcher_id IS NOT NULL
                AND (type IS NULL OR type = 1 OR (type = 2 AND EXISTS (SELECT 1 FROM crm_customer_friend cf WHERE cf.friend_id = f.friend_id)))
                AND record_date >= #{query.dateRange[0]}
                AND record_date &lt;= #{query.dateRange[1]}
                GROUP BY pitcher_id
            ) f ON pu.pitcher_id = f.pitcher_id
        LEFT JOIN
            (
                SELECT
                    f.pitcher_id,
                    COUNT(DISTINCT f.friend_id) AS customer_num
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                                            AND DATE(cf.begin_time) >= #{query.dateRange[0]}
                                            AND DATE(cf.begin_time) &lt;= #{query.dateRange[1]}
                GROUP BY f.pitcher_id
            ) cf ON pu.pitcher_id = cf.pitcher_id
        LEFT JOIN
            (
                SELECT
                    pitcher_id,
                    COUNT(cja.friend_id) AS handover_anchor_num
                FROM
                    (
                        SELECT
                            f.pitcher_id,
                            f.friend_id,
                            cja.receive_time
                        FROM pitcher_user pu
                        JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                        JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                        JOIN crm_customer_join_anchor cja ON cja.customer_id = cf.customer_id
                                                        AND cja.extend_id = cf.py_extend_id
                                                        AND DATE(cja.receive_time) >= DATE(cf.begin_time)
                                                        AND (DATE(cja.receive_time) &lt; DATE(cf.end_time) OR cf.end_time IS NULL)
                                                        AND cja.status = '1'
                        GROUP BY f.pitcher_id,f.friend_id
                    ) cja
                WHERE DATE(cja.receive_time) >= #{query.dateRange[0]} AND DATE(cja.receive_time) &lt;= #{query.dateRange[1]}
                GROUP BY cja.pitcher_id
            ) cja ON pu.pitcher_id = cja.pitcher_id
        LEFT JOIN
            (
                SELECT
                    pitcher_id,
                    COUNT(cjs.friend_id) AS handover_serve_num
                FROM
                    (
                        SELECT
                            f.pitcher_id,
                            f.friend_id,
                            cjs.receive_time
                        FROM pitcher_user pu
                        JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                        JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                        JOIN crm_customer_join_serve cjs ON cjs.customer_id = cf.customer_id
                                                        AND cjs.extend_id = cf.py_extend_id
                                                        AND DATE(cjs.receive_time) >= DATE(cf.begin_time)
                                                        AND (DATE(cjs.receive_time) &lt; DATE(cf.end_time) OR cf.end_time IS NULL)
                                                        AND cjs.status = '1'
                        GROUP BY f.pitcher_id,f.friend_id
                    ) cjs
                WHERE DATE(cjs.receive_time) >= #{query.dateRange[0]} AND DATE(cjs.receive_time) &lt;= #{query.dateRange[1]}
                GROUP BY cjs.pitcher_id
            ) cjs ON pu.pitcher_id = cjs.pitcher_id
        LEFT JOIN
            (
                SELECT
                    f.pitcher_id,
                    count(DISTINCT f.friend_id) AS first_charge_num
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                JOIN yooa_extend.extend_vermicelli ev ON f.friend_id = ev.friend_id
                                                    AND ev.fans_type = 5
                                                    AND DATE(ev.record_date) >= #{query.dateRange[0]}
                                                    AND DATE(ev.record_date) &lt;= #{query.dateRange[1]}
                GROUP BY f.pitcher_id
            ) ev ON pu.pitcher_id = ev.pitcher_id
        LEFT JOIN
            (
                SELECT
                    f.pitcher_id,
                    SUM(order_money) AS order_money
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                JOIN crm_customer_order co ON cf.customer_id = co.customer_id
                                        AND cf.py_extend_id = co.py_extend_id
                                        AND DATE(co.order_time) >= DATE(cf.begin_time)
                                        AND (DATE(co.order_time) &lt; DATE(cf.end_time) OR cf.end_time IS NULL)
                                        AND DATE(co.order_time) >= #{query.dateRange[0]}
                                        AND DATE(co.order_time) &lt;= #{query.dateRange[1]}
                                        AND co.order_status = '1'
                GROUP BY f.pitcher_id
            ) co ON pu.pitcher_id = co.pitcher_id
        LEFT JOIN
        (
        SELECT
        f.pitcher_id,
        SUM(order_money) AS order_money
        FROM pitcher_user pu
        JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
        JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
        JOIN crm_customer_order co ON cf.customer_id = co.customer_id
        AND cf.py_extend_id = co.py_extend_id
        AND DATE(co.order_time) >= DATE(cf.begin_time)
        AND (DATE(co.order_time) &lt; DATE(cf.end_time) OR cf.end_time IS NULL)
        AND DATE(co.order_time) >= #{query.dateRange[0]}
        AND DATE(co.order_time) &lt;= #{query.dateRange[1]}
        AND co.order_time >= DATE_SUB(NOW(), INTERVAL 45 DAY)
        AND co.order_status = '1'
        GROUP BY f.pitcher_id
        ) coxz ON pu.pitcher_id = coxz.pitcher_id
        LEFT JOIN
            (
                SELECT
                    pitcher_id,
                    SUM(fb_click) AS fb_click,
                    SUM(tk_friend) AS tk_friend,
                    SUM(tk_effective_friend) AS tk_effective_friend
                FROM yooa_crm.crm_cost
                WHERE cost_date >= #{query.dateRange[0]} AND cost_date &lt;= #{query.dateRange[1]}
                GROUP BY pitcher_id
            ) cost ON pu.pitcher_id = cost.pitcher_id
        LEFT JOIN
            (
                SELECT
                    f.pitcher_id,
                    COUNT(DISTINCT f.friend_id) AS quality_num
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                                            AND cf.quality_time IS NOT NULL
                                            AND DATE(cf.quality_time) >= #{query.dateRange[0]}
                                            AND DATE(cf.quality_time) &lt;= #{query.dateRange[1]}
                GROUP BY f.pitcher_id
            ) qf ON pu.pitcher_id = qf.pitcher_id
    </select>

    <select id="selectExtendPerformanceStatistics" resultType="com.yooa.crm.api.domain.vo.ExtendPerformanceVo">
        WITH pitcher_user AS (
            SELECT
                user_id AS pitcher_id
            FROM yooa_system.sys_user u
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
            <where>
                AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                <if test="query.pitcherName != null and query.pitcherName != ''">
                    AND u.nick_name like concat('%',#{query.pitcherName},'%')
                </if>
                <if test="query.pitcherDeptId != null">
                    AND (u.dept_id = #{query.pitcherDeptId} OR FIND_IN_SET(#{query.pitcherDeptId}, d.ancestors))
                </if>
                <if test="query.pitcherId != null">
                    AND u.user_id = #{query.pitcherId}
                </if>
                <if test="query.pitcherId == null">
                    ${query.params.dataScope}
                </if>
            </where>
        ),
        extend_user AS (
            SELECT
                user_id AS extend_id,
                nick_name AS extend_name,
                dept_name AS extend_dept_name,
                ancestors_names AS extend_dept_names
            FROM yooa_system.sys_user u
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
            <where>
                AND d.dept_type = '1'
                <if test="query.extendName != null and query.extendName != ''">
                    AND u.nick_name like concat('%',#{query.extendName},'%')
                </if>
                <if test="query.extendDeptId != null">
                    AND (u.dept_id = #{query.extendDeptId} OR FIND_IN_SET(#{query.extendDeptId}, d.ancestors))
                </if>
            </where>
        )

        SELECT
            IFNULL(SUM(f.friend_num),0) AS friendNum,
            IFNULL(SUM(cf.customer_num),0) AS customerNum,
            IFNULL(SUM(cja.join_anchor_num),0) AS handoverAnchorNum,
            IFNULL(SUM(cjs.join_serve_num),0) AS handoverServeNum,
            IFNULL(SUM(ev.first_charge),0) AS firstChargeNum,
            IFNULL(SUM(ev.fans2h),0) AS fans2hNum,
            IFNULL(SUM(ev.fans5k),0) AS fans5kNum,
            IFNULL(SUM(ev.fans5w),0) AS fans5wNum,
            IFNULL(SUM(co.order_money),0) AS orderMoney,
            IFNULL(SUM(coxz.order_money),0) AS orderXzMoney,
            IFNULL(SUM(qf.quality_num),0) AS qualityNum
        FROM
            extend_user eu
        LEFT JOIN
            (
                SELECT
                    f.extend_id,
                    COUNT(f.friend_id) AS friend_num
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                AND (type IS NULL OR type = 1 OR (type = 2 AND EXISTS (SELECT 1 FROM crm_customer_friend cf WHERE cf.friend_id = f.friend_id)))
                    AND record_date >= #{query.dateRange[0]}
                    AND record_date &lt;= #{query.dateRange[1]}
                GROUP BY f.extend_id
            ) f ON eu.extend_id = f.extend_id
        LEFT JOIN
            (
                SELECT
                    f.extend_id,
                    COUNT(DISTINCT f.friend_id) AS customer_num
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                    AND DATE(cf.begin_time) >= #{query.dateRange[0]}
                    AND DATE(cf.begin_time) &lt;= #{query.dateRange[1]}
                GROUP BY f.extend_id
            ) cf ON eu.extend_id = cf.extend_id
        LEFT JOIN
            (
                SELECT
                    extend_id,
                    COUNT(cja.friend_id) AS join_anchor_num
                FROM
                    (
                        SELECT
                            f.extend_id,
                            f.friend_id,
                            cja.receive_time
                        FROM pitcher_user pu
                        JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                        JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                        JOIN crm_customer_join_anchor cja ON cja.customer_id = cf.customer_id
                            AND cja.extend_id = cf.py_extend_id
                            AND DATE(cja.receive_time) >= DATE(cf.begin_time)
                            AND (DATE(cja.receive_time) &lt; DATE(cf.end_time) OR cf.end_time IS NULL)
                            AND cja.status = '1'
                        GROUP BY f.extend_id,f.friend_id
                    ) cja
                WHERE DATE(cja.receive_time) >= #{query.dateRange[0]}
                AND DATE(cja.receive_time) &lt;= #{query.dateRange[1]}
                GROUP BY cja.extend_id
            ) cja ON eu.extend_id = cja.extend_id
        LEFT JOIN
            (
                SELECT
                    extend_id,
                    COUNT(cjs.friend_id) AS join_serve_num
                FROM
                    (
                        SELECT
                            f.extend_id,
                            f.friend_id,
                            cjs.receive_time
                        FROM pitcher_user pu
                        JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                        JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                        JOIN crm_customer_join_serve cjs ON cjs.customer_id = cf.customer_id
                            AND cjs.extend_id = cf.py_extend_id
                            AND DATE(cjs.receive_time) >= DATE(cf.begin_time)
                            AND (DATE(cjs.receive_time) &lt; DATE(cf.end_time) OR cf.end_time IS NULL)
                            AND cjs.status = '1'
                        GROUP BY f.extend_id,f.friend_id
                    ) cjs
                WHERE DATE(cjs.receive_time) >= #{query.dateRange[0]}
                AND DATE(cjs.receive_time) &lt;= #{query.dateRange[1]}
                GROUP BY cjs.extend_id
            ) cjs ON eu.extend_id = cjs.extend_id
        LEFT JOIN
            (
                SELECT
                    f.extend_id,
                    SUM(CASE WHEN ev.fans_type = 0 THEN 1 ELSE 0 END) AS fans2h,
                    SUM(CASE WHEN ev.fans_type = 2 THEN 1 ELSE 0 END) AS fans5k,
                    SUM(CASE WHEN ev.fans_type = 3 THEN 1 ELSE 0 END) AS fans5w,
                    SUM(CASE WHEN ev.fans_type = 5 THEN 1 ELSE 0 END) AS first_charge
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                JOIN yooa_extend.extend_vermicelli ev ON f.friend_id = ev.friend_id
                    AND DATE(ev.record_date) >= #{query.dateRange[0]}
                    AND DATE(ev.record_date) &lt;= #{query.dateRange[1]}
                GROUP BY f.extend_id
            ) ev ON eu.extend_id = ev.extend_id
        LEFT JOIN
            (
                SELECT
                    f.extend_id,
                    SUM(order_money) AS order_money
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                JOIN crm_customer_order co ON cf.customer_id = co.customer_id
                    AND cf.py_extend_id = co.py_extend_id
                    AND DATE(co.order_time) >= DATE(cf.begin_time)
                    AND (DATE(co.order_time) &lt; DATE(cf.end_time) OR cf.end_time IS NULL)
                    AND DATE(co.order_time) >= #{query.dateRange[0]}
                    AND DATE(co.order_time) &lt;= #{query.dateRange[1]}
                    AND co.order_status = '1'
                GROUP BY f.extend_id
            ) co ON eu.extend_id = co.extend_id
        LEFT JOIN
        (
        SELECT
        f.extend_id,
        SUM(order_money) AS order_money
        FROM pitcher_user pu
        JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
        JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
        JOIN crm_customer_order co ON cf.customer_id = co.customer_id
        AND cf.py_extend_id = co.py_extend_id
        AND DATE(co.order_time) >= DATE(cf.begin_time)
        AND (DATE(co.order_time) &lt; DATE(cf.end_time) OR cf.end_time IS NULL)
        AND DATE(co.order_time) >= #{query.dateRange[0]}
        AND DATE(co.order_time) &lt;= #{query.dateRange[1]}
        AND co.order_time >= DATE_SUB(NOW(), INTERVAL 45 DAY)
        AND co.order_status = '1'
        GROUP BY f.extend_id
        ) coxz ON eu.extend_id = coxz.extend_id
        LEFT JOIN
            (
                SELECT
                    f.extend_id,
                    COUNT(DISTINCT f.friend_id) AS quality_num
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                    AND cf.quality_time IS NOT NULL
                    AND DATE(cf.quality_time) >= #{query.dateRange[0]}
                    AND DATE(cf.quality_time) &lt;= #{query.dateRange[1]}
                GROUP BY f.extend_id
            ) qf ON eu.extend_id = qf.extend_id
        WHERE f.friend_num IS NOT NULL
        OR cf.customer_num IS NOT NULL
        OR cja.join_anchor_num IS NOT NULL
        OR cjs.join_serve_num IS NOT NULL
        OR ev.first_charge IS NOT NULL
        OR ev.fans2h IS NOT NULL
        OR ev.fans5k IS NOT NULL
        OR ev.fans5w IS NOT NULL
        OR co.order_money IS NOT NULL
        OR coxz.order_money IS NOT NULL
        OR qf.quality_num IS NOT NULL
    </select>

    <select id="selectExtendPerformance" resultType="com.yooa.crm.api.domain.vo.ExtendPerformanceVo">
        WITH pitcher_user AS (
            SELECT
                user_id AS pitcher_id
            FROM yooa_system.sys_user u
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
            <where>
                AND (u.dept_id = #{query.tfType} OR FIND_IN_SET(#{query.tfType}, d.ancestors))
                <if test="query.pitcherName != null and query.pitcherName != ''">
                    AND u.nick_name like concat('%',#{query.pitcherName},'%')
                </if>
                <if test="query.pitcherDeptId != null">
                    AND (u.dept_id = #{query.pitcherDeptId} OR FIND_IN_SET(#{query.pitcherDeptId}, d.ancestors))
                </if>
                <if test="query.pitcherId != null">
                    AND u.user_id = #{query.pitcherId}
                </if>
                <if test="query.pitcherId == null">
                    ${query.params.dataScope}
                </if>
            </where>
        ),
        extend_user AS (
            SELECT
             user_id AS extend_id,
             nick_name AS extend_name,
             dept_name AS extend_dept_name,
             ancestors_names AS extend_dept_names
            FROM yooa_system.sys_user u
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
            <where>
            AND d.dept_type = '1'
            <if test="query.extendName != null and query.extendName != ''">
                AND u.nick_name like concat('%',#{query.extendName},'%')
            </if>
            <if test="query.extendDeptId != null">
                AND (u.dept_id = #{query.extendDeptId} OR FIND_IN_SET(#{query.extendDeptId}, d.ancestors))
            </if>
            </where>
        )

        SELECT
            eu.*,
            IFNULL(f.friend_num,0) AS friendNum,
            IFNULL(cf.customer_num,0) AS customerNum,
            IFNULL(cja.join_anchor_num,0) AS handoverAnchorNum,
            IFNULL(cjs.join_serve_num,0) AS handoverServeNum,
            IFNULL(ev.first_charge,0) AS firstChargeNum,
            IFNULL(ev.fans2h,0) AS fans2hNum,
            IFNULL(ev.fans5k,0) AS fans5kNum,
            IFNULL(ev.fans5w,0) AS fans5wNum,
            IFNULL(co.order_money,0) AS orderMoney,
            IFNULL(coxz.order_money,0) AS orderXzMoney,
            IFNULL(qf.quality_num,0) AS qualityNum
        FROM
            extend_user eu
        LEFT JOIN
            (
                SELECT
                    f.extend_id,
                    COUNT(f.friend_id) AS friend_num
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                AND (type IS NULL OR type = 1 OR (type = 2 AND EXISTS (SELECT 1 FROM crm_customer_friend cf WHERE cf.friend_id = f.friend_id)))
                    AND record_date >= #{query.dateRange[0]}
                    AND record_date &lt;= #{query.dateRange[1]}
                GROUP BY f.extend_id
            ) f ON eu.extend_id = f.extend_id
        LEFT JOIN
            (
                SELECT
                    f.extend_id,
                    COUNT(DISTINCT f.friend_id) AS customer_num
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                    AND DATE(cf.begin_time) >= #{query.dateRange[0]}
                    AND DATE(cf.begin_time) &lt;= #{query.dateRange[1]}
                GROUP BY f.extend_id
            ) cf ON eu.extend_id = cf.extend_id
        LEFT JOIN
            (
                SELECT
                    extend_id,
                    COUNT(cja.friend_id) AS join_anchor_num
                FROM
                    (
                        SELECT
                            f.extend_id,
                            f.friend_id,
                            cja.receive_time
                        FROM pitcher_user pu
                        JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                        JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                        JOIN crm_customer_join_anchor cja ON cja.customer_id = cf.customer_id
                            AND cja.extend_id = cf.py_extend_id
                            AND DATE(cja.receive_time) >= DATE(cf.begin_time)
                            AND (DATE(cja.receive_time) &lt; DATE(cf.end_time) OR cf.end_time IS NULL)
                            AND cja.status = '1'
                        GROUP BY f.extend_id,f.friend_id
                    ) cja
                WHERE DATE(cja.receive_time) >= #{query.dateRange[0]}
                AND DATE(cja.receive_time) &lt;= #{query.dateRange[1]}
                GROUP BY cja.extend_id
            ) cja ON eu.extend_id = cja.extend_id
        LEFT JOIN
            (
                SELECT
                    extend_id,
                    COUNT(cjs.friend_id) AS join_serve_num
                FROM
                    (
                        SELECT
                            f.extend_id,
                            f.friend_id,
                            cjs.receive_time
                        FROM pitcher_user pu
                        JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                        JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                        JOIN crm_customer_join_serve cjs ON cjs.customer_id = cf.customer_id
                            AND cjs.extend_id = cf.py_extend_id
                            AND DATE(cjs.receive_time) >= DATE(cf.begin_time)
                            AND (DATE(cjs.receive_time) &lt; DATE(cf.end_time) OR cf.end_time IS NULL)
                            AND cjs.status = '1'
                        GROUP BY f.extend_id,f.friend_id
                    ) cjs
                WHERE DATE(cjs.receive_time) >= #{query.dateRange[0]}
                AND DATE(cjs.receive_time) &lt;= #{query.dateRange[1]}
                GROUP BY cjs.extend_id
            ) cjs ON eu.extend_id = cjs.extend_id
        LEFT JOIN
            (
                SELECT
                    f.extend_id,
                    SUM(CASE WHEN ev.fans_type = 0 THEN 1 ELSE 0 END) AS fans2h,
                    SUM(CASE WHEN ev.fans_type = 2 THEN 1 ELSE 0 END) AS fans5k,
                    SUM(CASE WHEN ev.fans_type = 3 THEN 1 ELSE 0 END) AS fans5w,
                    SUM(CASE WHEN ev.fans_type = 5 THEN 1 ELSE 0 END) AS first_charge
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                JOIN yooa_extend.extend_vermicelli ev ON f.friend_id = ev.friend_id
                    AND DATE(ev.record_date) >= #{query.dateRange[0]}
                    AND DATE(ev.record_date) &lt;= #{query.dateRange[1]}
                GROUP BY f.extend_id
            ) ev ON eu.extend_id = ev.extend_id
        LEFT JOIN
            (
                SELECT
                    f.extend_id,
                    SUM(order_money) AS order_money
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                JOIN crm_customer_order co ON cf.customer_id = co.customer_id
                    AND cf.py_extend_id = co.py_extend_id
                    AND DATE(co.order_time) >= DATE(cf.begin_time)
                    AND (DATE(co.order_time) &lt; DATE(cf.end_time) OR cf.end_time IS NULL)
                    AND DATE(co.order_time) >= #{query.dateRange[0]}
                    AND DATE(co.order_time) &lt;= #{query.dateRange[1]}
                    AND co.order_status = '1'
                GROUP BY f.extend_id
            ) co ON eu.extend_id = co.extend_id
        LEFT JOIN
        (
        SELECT
        f.extend_id,
        SUM(order_money) AS order_money
        FROM pitcher_user pu
        JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
        JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
        JOIN crm_customer_order co ON cf.customer_id = co.customer_id
        AND cf.py_extend_id = co.py_extend_id
        AND DATE(co.order_time) >= DATE(cf.begin_time)
        AND (DATE(co.order_time) &lt; DATE(cf.end_time) OR cf.end_time IS NULL)
        AND DATE(co.order_time) >= #{query.dateRange[0]}
        AND DATE(co.order_time) &lt;= #{query.dateRange[1]}
        AND co.order_time >= DATE_SUB(NOW(), INTERVAL 45 DAY)
        AND co.order_status = '1'
        GROUP BY f.extend_id
        ) coxz ON eu.extend_id = coxz.extend_id
        LEFT JOIN
            (
                SELECT
                    f.extend_id,
                    COUNT(DISTINCT f.friend_id) AS quality_num
                FROM pitcher_user pu
                JOIN crm_friend f ON pu.pitcher_id = f.pitcher_id
                JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
                    AND cf.quality_time IS NOT NULL
                    AND DATE(cf.quality_time) >= #{query.dateRange[0]}
                    AND DATE(cf.quality_time) &lt;= #{query.dateRange[1]}
                GROUP BY f.extend_id
            ) qf ON eu.extend_id = qf.extend_id
        WHERE f.friend_num IS NOT NULL
        OR cf.customer_num IS NOT NULL
        OR cja.join_anchor_num IS NOT NULL
        OR cjs.join_serve_num IS NOT NULL
        OR ev.first_charge IS NOT NULL
        OR ev.fans2h IS NOT NULL
        OR ev.fans5k IS NOT NULL
        OR ev.fans5w IS NOT NULL
        OR co.order_money IS NOT NULL
        OR coxz.order_money IS NOT NULL
        OR qf.quality_num IS NOT NULL
    </select>

    <select id="selectExtendRoiStatisticsGroupByExtend" resultType="com.yooa.crm.api.domain.vo.ExtendRoiVo">
        SELECT
            IFNULL(SUM(cost_data.pitcher_num), 0) AS pitcherNum,
            IFNULL(SUM(cost_data.money_cny), 0) AS moneyCny,
            IFNULL(SUM(cost_data.fb_click), 0) AS fbClick,
            IFNULL(SUM(cost_data.tk_friend), 0) AS tkFriend,
            IFNULL(SUM(cost_data.tk_effective_friend), 0) AS tkEffectiveFriend,
            IFNULL(SUM(roi_data.friend_num), 0) AS friendNum,
            IFNULL(SUM(roi_data.customer_num), 0) AS customerNum,
            IFNULL(SUM(roi_data.recharge_num), 0) AS rechargeNum,
            IFNULL(ROUND(SUM(roi_data.recharge_num) / SUM(roi_data.customer_num) * 100, 2), 0) AS rechargeRate,
            IFNULL(ROUND(SUM(roi_data.ltv_total) / SUM(roi_data.customer_num), 2), 0) AS arpu,
            IFNULL(ROUND(SUM(roi_data.ltv_total) / SUM(roi_data.recharge_num), 2), 0) AS arppu,
            IFNULL(ROUND(SUM(roi_data.ltv_total) - IFNULL(SUM(cost_data.money_cny), 0), 2), 0) AS profit,
            IFNULL(ROUND(SUM(roi_data.ltv_total) / IFNULL(SUM(cost_data.money_cny), 0) * 100, 2), 0) AS profitRate,
            IFNULL(SUM(roi_data.ltv_1),0) AS ltv1,
            IFNULL(SUM(roi_data.ltv_2),0) AS ltv2,
            IFNULL(SUM(roi_data.ltv_3),0) AS ltv3,
            IFNULL(SUM(roi_data.ltv_4),0) AS ltv4,
            IFNULL(SUM(roi_data.ltv_5),0) AS ltv5,
            IFNULL(SUM(roi_data.ltv_6),0) AS ltv6,
            IFNULL(SUM(roi_data.ltv_7),0) AS ltv7,
            IFNULL(SUM(roi_data.ltv_15),0) AS ltv15,
            IFNULL(SUM(roi_data.ltv_30),0) AS ltv30,
            IFNULL(SUM(roi_data.ltv_45),0) AS ltv45,
            IFNULL(SUM(roi_data.ltv_60),0) AS ltv60,
            IFNULL(SUM(roi_data.ltv_90),0) AS ltv90,
            IFNULL(SUM(roi_data.ltv_120),0) AS ltv120,
            IFNULL(SUM(roi_data.ltv_150),0) AS ltv150,
            IFNULL(SUM(roi_data.ltv_180),0) AS ltv180,
            IFNULL(SUM(roi_data.ltv_210),0) AS ltv210,
            IFNULL(SUM(roi_data.ltv_240),0) AS ltv240,
            IFNULL(SUM(roi_data.ltv_270),0) AS ltv270,
            IFNULL(SUM(roi_data.ltv_300),0) AS ltv300,
            IFNULL(SUM(roi_data.ltv_330),0) AS ltv330,
            IFNULL(SUM(roi_data.ltv_360),0) AS ltv360,
            IFNULL(SUM(roi_data.ltv_total),0) AS ltvTotal
        FROM
            (
                SELECT
                    su.extend_id AS extend_id,
                    su.extend_name AS extend_name,
                    su.extend_dept_name AS extend_dept_name,
                    su.extend_dept_names AS extend_dept_names,
                    IFNULL(SUM(base_data.friend_num),0) AS friend_num,
                    IFNULL(SUM(base_data.customer_num),0) AS customer_num,
                    SUM(CASE WHEN base_data.ltv_total != 0 THEN 1 ELSE 0 END) AS recharge_num,
                    IFNULL(SUM(base_data.ltv_1),0) AS ltv_1,
                    IFNULL(SUM(base_data.ltv_2),0) AS ltv_2,
                    IFNULL(SUM(base_data.ltv_3),0) AS ltv_3,
                    IFNULL(SUM(base_data.ltv_4),0) AS ltv_4,
                    IFNULL(SUM(base_data.ltv_5),0) AS ltv_5,
                    IFNULL(SUM(base_data.ltv_6),0) AS ltv_6,
                    IFNULL(SUM(base_data.ltv_7),0) AS ltv_7,
                    IFNULL(SUM(base_data.ltv_15),0) AS ltv_15,
                    IFNULL(SUM(base_data.ltv_30),0) AS ltv_30,
                    IFNULL(SUM(base_data.ltv_45),0) AS ltv_45,
                    IFNULL(SUM(base_data.ltv_60),0) AS ltv_60,
                    IFNULL(SUM(base_data.ltv_90),0) AS ltv_90,
                    IFNULL(SUM(base_data.ltv_120),0) AS ltv_120,
                    IFNULL(SUM(base_data.ltv_150),0) AS ltv_150,
                    IFNULL(SUM(base_data.ltv_180),0) AS ltv_180,
                    IFNULL(SUM(base_data.ltv_210),0) AS ltv_210,
                    IFNULL(SUM(base_data.ltv_240),0) AS ltv_240,
                    IFNULL(SUM(base_data.ltv_270),0) AS ltv_270,
                    IFNULL(SUM(base_data.ltv_300),0) AS ltv_300,
                    IFNULL(SUM(base_data.ltv_330),0) AS ltv_330,
                    IFNULL(SUM(base_data.ltv_360),0) AS ltv_360,
                    IFNULL(SUM(base_data.ltv_total),0) AS ltv_total
                FROM
                    (
                        SELECT
                            user_id AS extend_id,
                            nick_name AS extend_name,
                            dept_name AS extend_dept_name,
                            ancestors_names AS extend_dept_names
                        FROM yooa_system.sys_user u
                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                        <where>
                            AND dept_type = 1
                            <if test="query.extendName != null and query.extendName != ''">
                                AND u.nick_name like concat('%',#{query.extendName},'%')
                            </if>
                            <if test="query.extendDeptId != null">
                                AND (u.dept_id = #{query.extendDeptId} OR FIND_IN_SET(#{query.extendDeptId}, d.ancestors))
                            </if>
                            ${query.params.dataScope}
                        </where>
                    ) su
                LEFT JOIN
                    (
                        SELECT
                            f.friend_id AS friend_id,
                            f.extend_id AS extend_id,
                            COUNT(DISTINCT f.friend_id) AS friend_num,
                            COUNT(DISTINCT cf.friend_id) AS customer_num,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 0 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 0 DAY) THEN co.order_money ELSE 0 END) AS ltv_1,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 1 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 1 DAY) THEN co.order_money ELSE 0 END) AS ltv_2,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 2 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 2 DAY) THEN co.order_money ELSE 0 END) AS ltv_3,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 3 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 3 DAY) THEN co.order_money ELSE 0 END) AS ltv_4,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 4 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 4 DAY) THEN co.order_money ELSE 0 END) AS ltv_5,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 5 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 5 DAY) THEN co.order_money ELSE 0 END) AS ltv_6,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 6 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 6 DAY) THEN co.order_money ELSE 0 END) AS ltv_7,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 14 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 14 DAY) THEN co.order_money ELSE 0 END) AS ltv_15,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 29 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 29 DAY) THEN co.order_money ELSE 0 END) AS ltv_30,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 44 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 44 DAY) THEN co.order_money ELSE 0 END) AS ltv_45,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 59 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 59 DAY) THEN co.order_money ELSE 0 END) AS ltv_60,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 89 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 89 DAY) THEN co.order_money ELSE 0 END) AS ltv_90,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 119 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 119 DAY) THEN co.order_money ELSE 0 END) AS ltv_120,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 149 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 149 DAY) THEN co.order_money ELSE 0 END) AS ltv_150,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 179 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 179 DAY) THEN co.order_money ELSE 0 END) AS ltv_180,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 209 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 209 DAY) THEN co.order_money ELSE 0 END) AS ltv_210,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 239 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 239 DAY) THEN co.order_money ELSE 0 END) AS ltv_240,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 269 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 269 DAY) THEN co.order_money ELSE 0 END) AS ltv_270,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 299 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 299 DAY) THEN co.order_money ELSE 0 END) AS ltv_300,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 329 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 329 DAY) THEN co.order_money ELSE 0 END) AS ltv_330,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 359 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 359 DAY) THEN co.order_money ELSE 0 END) AS ltv_360,
                            SUM(CASE WHEN co.order_date >= f.record_date THEN co.order_money ELSE 0 END) AS ltv_total
                        FROM
                            (
                                SELECT
                                    friend_id,
                                    extend_id,
                                    record_date
                                FROM crm_friend
                                <where>
                                    <if test="query.mainChannelId != null">
                                        AND main_channel_id = #{query.mainChannelId}
                                    </if>
                                    <if test="query.subChannelId != null">
                                        AND sub_channel_id = #{query.subChannelId}
                                    </if>
                                    <if test="query.language != null and query.language != ''">
                                        AND language = #{query.language}
                                    </if>
                                    <if test="query.sex != null and query.sex != ''">
                                        AND sex = #{query.sex}
                                    </if>
                                    AND record_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                                </where>
                            ) f
                        LEFT JOIN
                            (
                                SELECT
                                    friend_id,
                                    customer_id,
                                    py_extend_id,
                                    DATE(begin_time) AS begin_date,
                                    DATE(end_time) AS end_date
                                FROM crm_customer_friend
                            ) cf ON f.friend_id = cf.friend_id
                        LEFT JOIN
                            (
                                SELECT
                                    customer_id,
                                    py_extend_id,
                                    order_money,
                                    DATE(order_time) AS order_date
                                FROM crm_customer_order
                            ) co ON co.customer_id = cf.customer_id AND co.py_extend_id = cf.py_extend_id
                                    AND co.order_date >= cf.begin_date AND (co.order_date &lt; cf.end_date OR cf.end_date IS NULL)
                        GROUP BY
                        f.friend_id,
                        f.extend_id
                    ) base_data ON su.extend_id = base_data.extend_id
                GROUP BY
                su.extend_id
            ) roi_data
        LEFT JOIN
            (
                SELECT
                    extend_id,
                    COUNT(DISTINCT cost_date) AS pitcher_num,
                    SUM(money_cny) AS money_cny,
                    SUM(fb_click) AS fb_click,
                    SUM(tk_friend) AS tk_friend,
                    SUM(tk_effective_friend) AS tk_effective_friend
                FROM yooa_crm.crm_cost
                <where>
                    AND cost_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                    <if test="query.extendId != null">
                        AND extend_id = #{query.extendId}
                    </if>
                    <if test="query.mainChannelId != null">
                        AND main_channel_id = #{query.mainChannelId}
                    </if>
                    <if test="query.subChannelId != null">
                        AND sub_channel_id = #{query.subChannelId}
                    </if>
                    <if test="query.language != null and query.language != ''">
                        AND language = #{query.language}
                    </if>
                    <if test="query.sex != null and query.sex != ''">
                        AND sex = #{query.sex}
                    </if>
                </where>
                GROUP BY
                extend_id
            ) cost_data ON roi_data.extend_id = cost_data.extend_id
        WHERE roi_data.friend_num != 0
            OR (cost_data.money_cny != 0 AND cost_data.money_cny IS NOT NULL )
            OR (cost_data.fb_click != 0 AND cost_data.fb_click IS NOT NULL )
            OR (cost_data.tk_friend != 0 AND cost_data.tk_friend IS NOT NULL )
            OR (cost_data.tk_effective_friend != 0 AND cost_data.tk_effective_friend IS NOT NULL )
    </select>

    <select id="selectExtendRoiGroupByExtend" resultType="com.yooa.crm.api.domain.vo.ExtendRoiVo">
        SELECT
            roi_data.*,
            IFNULL(cost_data.pitcher_num, 0) AS pitcherNum,
            IFNULL(cost_data.money_cny, 0) AS moneyCny,
            IFNULL(cost_data.fb_click, 0) AS fbClick,
            IFNULL(cost_data.tk_friend, 0) AS tkFriend,
            IFNULL(cost_data.tk_effective_friend, 0) AS tkEffectiveFriend,
            IFNULL(ROUND(roi_data.rechargeNum / roi_data.customerNum * 100, 2), 0) AS rechargeRate,
            IFNULL(ROUND(roi_data.ltvTotal / roi_data.customerNum, 2), 0) AS arpu,
            IFNULL(ROUND(roi_data.ltvTotal / roi_data.rechargeNum, 2), 0) AS arppu,
            IFNULL(ROUND(roi_data.ltvTotal - IFNULL(cost_data.money_cny, 0), 2), 0) AS profit,
            IFNULL(ROUND(roi_data.ltvTotal / IFNULL(cost_data.money_cny, 0) * 100, 2), 0) AS profitRate
        FROM
            (
                SELECT
                    su.extend_id AS extend_id,
                    su.extend_name AS extend_name,
                    su.extend_dept_name AS extend_dept_name,
                    su.extend_dept_names AS extend_dept_names,
                    IFNULL(SUM(base_data.friend_num),0) AS friendNum,
                    IFNULL(SUM(base_data.customer_num),0) AS customerNum,
                    SUM(CASE WHEN base_data.ltv_total != 0 THEN 1 ELSE 0 END) AS rechargeNum,
                    IFNULL(SUM(base_data.ltv_1),0) AS ltv_1,
                    IFNULL(SUM(base_data.ltv_2),0) AS ltv_2,
                    IFNULL(SUM(base_data.ltv_3),0) AS ltv_3,
                    IFNULL(SUM(base_data.ltv_4),0) AS ltv_4,
                    IFNULL(SUM(base_data.ltv_5),0) AS ltv_5,
                    IFNULL(SUM(base_data.ltv_6),0) AS ltv_6,
                    IFNULL(SUM(base_data.ltv_7),0) AS ltv_7,
                    IFNULL(SUM(base_data.ltv_15),0) AS ltv_15,
                    IFNULL(SUM(base_data.ltv_30),0) AS ltv_30,
                    IFNULL(SUM(base_data.ltv_45),0) AS ltv_45,
                    IFNULL(SUM(base_data.ltv_60),0) AS ltv_60,
                    IFNULL(SUM(base_data.ltv_90),0) AS ltv_90,
                    IFNULL(SUM(base_data.ltv_120),0) AS ltv_120,
                    IFNULL(SUM(base_data.ltv_150),0) AS ltv_150,
                    IFNULL(SUM(base_data.ltv_180),0) AS ltv_180,
                    IFNULL(SUM(base_data.ltv_210),0) AS ltv_210,
                    IFNULL(SUM(base_data.ltv_240),0) AS ltv_240,
                    IFNULL(SUM(base_data.ltv_270),0) AS ltv_270,
                    IFNULL(SUM(base_data.ltv_300),0) AS ltv_300,
                    IFNULL(SUM(base_data.ltv_330),0) AS ltv_330,
                    IFNULL(SUM(base_data.ltv_360),0) AS ltv_360,
                    IFNULL(SUM(base_data.ltv_total),0) AS ltvTotal
                FROM
                    (
                        SELECT
                            user_id AS extend_id,
                            nick_name AS extend_name,
                            dept_name AS extend_dept_name,
                            ancestors_names AS extend_dept_names
                        FROM yooa_system.sys_user u
                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                        <where>
                            AND dept_type = 1
                            <if test="query.extendName != null and query.extendName != ''">
                                AND u.nick_name like concat('%',#{query.extendName},'%')
                            </if>
                            <if test="query.extendDeptId != null">
                                AND (u.dept_id = #{query.extendDeptId} OR FIND_IN_SET(#{query.extendDeptId}, d.ancestors))
                            </if>
                            ${query.params.dataScope}
                        </where>
                    ) su
                LEFT JOIN
                    (
                        SELECT
                            f.friend_id AS friend_id,
                            f.extend_id AS extend_id,
                            COUNT(DISTINCT f.friend_id) AS friend_num,
                            COUNT(DISTINCT cf.friend_id) AS customer_num,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 0 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 0 DAY) THEN co.order_money ELSE 0 END) AS ltv_1,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 1 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 1 DAY) THEN co.order_money ELSE 0 END) AS ltv_2,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 2 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 2 DAY) THEN co.order_money ELSE 0 END) AS ltv_3,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 3 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 3 DAY) THEN co.order_money ELSE 0 END) AS ltv_4,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 4 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 4 DAY) THEN co.order_money ELSE 0 END) AS ltv_5,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 5 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 5 DAY) THEN co.order_money ELSE 0 END) AS ltv_6,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 6 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 6 DAY) THEN co.order_money ELSE 0 END) AS ltv_7,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 14 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 14 DAY) THEN co.order_money ELSE 0 END) AS ltv_15,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 29 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 29 DAY) THEN co.order_money ELSE 0 END) AS ltv_30,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 44 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 44 DAY) THEN co.order_money ELSE 0 END) AS ltv_45,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 59 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 59 DAY) THEN co.order_money ELSE 0 END) AS ltv_60,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 89 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 89 DAY) THEN co.order_money ELSE 0 END) AS ltv_90,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 119 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 119 DAY) THEN co.order_money ELSE 0 END) AS ltv_120,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 149 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 149 DAY) THEN co.order_money ELSE 0 END) AS ltv_150,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 179 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 179 DAY) THEN co.order_money ELSE 0 END) AS ltv_180,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 209 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 209 DAY) THEN co.order_money ELSE 0 END) AS ltv_210,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 239 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 239 DAY) THEN co.order_money ELSE 0 END) AS ltv_240,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 269 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 269 DAY) THEN co.order_money ELSE 0 END) AS ltv_270,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 299 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 299 DAY) THEN co.order_money ELSE 0 END) AS ltv_300,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 329 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 329 DAY) THEN co.order_money ELSE 0 END) AS ltv_330,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 359 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 359 DAY) THEN co.order_money ELSE 0 END) AS ltv_360,
                            SUM(CASE WHEN co.order_date >= f.record_date THEN co.order_money ELSE 0 END) AS ltv_total
                        FROM
                            (
                                SELECT
                                    friend_id,
                                    extend_id,
                                    record_date
                                FROM crm_friend
                                <where>
                                    <if test="query.mainChannelId != null">
                                        AND main_channel_id = #{query.mainChannelId}
                                    </if>
                                    <if test="query.subChannelId != null">
                                        AND sub_channel_id = #{query.subChannelId}
                                    </if>
                                    <if test="query.language != null and query.language != ''">
                                        AND language = #{query.language}
                                    </if>
                                    <if test="query.sex != null and query.sex != ''">
                                        AND sex = #{query.sex}
                                    </if>
                                    AND record_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                                </where>
                            ) f
                        LEFT JOIN
                            (
                                SELECT
                                    friend_id,
                                    customer_id,
                                    py_extend_id,
                                    DATE(begin_time) AS begin_date,
                                    DATE(end_time) AS end_date
                                FROM crm_customer_friend
                            ) cf ON f.friend_id = cf.friend_id
                        LEFT JOIN
                            (
                                SELECT
                                    customer_id,
                                    py_extend_id,
                                    order_money,
                                    DATE(order_time) AS order_date
                                FROM crm_customer_order
                            ) co ON co.customer_id = cf.customer_id AND co.py_extend_id = cf.py_extend_id
                                    AND co.order_date >= cf.begin_date AND (co.order_date &lt; cf.end_date OR cf.end_date IS NULL)
                        GROUP BY
                        f.friend_id,
                        f.extend_id
                    ) base_data ON su.extend_id = base_data.extend_id
                GROUP BY
                su.extend_id
            ) roi_data
        LEFT JOIN
            (
                SELECT
                    extend_id,
                    COUNT(DISTINCT cost_date) AS pitcher_num,
                    SUM(money_cny) AS money_cny,
                    SUM(fb_click) AS fb_click,
                    SUM(tk_friend) AS tk_friend,
                    SUM(tk_effective_friend) AS tk_effective_friend
                FROM yooa_crm.crm_cost
                <where>
                    AND cost_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                    <if test="query.extendId != null">
                        AND extend_id = #{query.extendId}
                    </if>
                    <if test="query.mainChannelId != null">
                        AND main_channel_id = #{query.mainChannelId}
                    </if>
                    <if test="query.subChannelId != null">
                        AND sub_channel_id = #{query.subChannelId}
                    </if>
                    <if test="query.language != null and query.language != ''">
                        AND language = #{query.language}
                    </if>
                    <if test="query.sex != null and query.sex != ''">
                        AND sex = #{query.sex}
                    </if>
                </where>
                GROUP BY
                extend_id
            ) cost_data ON roi_data.extend_id = cost_data.extend_id
        WHERE roi_data.friendNum != 0
            OR (cost_data.money_cny != 0 AND cost_data.money_cny IS NOT NULL )
            OR (cost_data.fb_click != 0 AND cost_data.fb_click IS NOT NULL )
            OR (cost_data.tk_friend != 0 AND cost_data.tk_friend IS NOT NULL )
            OR (cost_data.tk_effective_friend != 0 AND cost_data.tk_effective_friend IS NOT NULL )
    </select>

    <select id="selectExtendRoiStatisticsGroupByDate" resultType="com.yooa.crm.api.domain.vo.DateRoiVo">
        SELECT
            IFNULL(SUM(cost_data.pitcher_num), 0) AS pitcherNum,
            IFNULL(SUM(cost_data.money_cny), 0) AS moneyCny,
            IFNULL(SUM(cost_data.fb_click), 0) AS fbClick,
            IFNULL(SUM(cost_data.tk_friend), 0) AS tkFriend,
            IFNULL(SUM(cost_data.tk_effective_friend), 0) AS tkEffectiveFriend,
            IFNULL(SUM(roi_data.friend_num), 0) AS friendNum,
            IFNULL(SUM(roi_data.customer_num), 0) AS customerNum,
            IFNULL(SUM(roi_data.recharge_num), 0) AS rechargeNum,
            IFNULL(ROUND(SUM(roi_data.recharge_num) / SUM(roi_data.customer_num) * 100, 2), 0) AS rechargeRate,
            IFNULL(ROUND(SUM(roi_data.ltv_total) / SUM(roi_data.customer_num), 2), 0) AS arpu,
            IFNULL(ROUND(SUM(roi_data.ltv_total) / SUM(roi_data.recharge_num), 2), 0) AS arppu,
            IFNULL(ROUND(SUM(roi_data.ltv_total) - IFNULL(SUM(cost_data.money_cny), 0), 2), 0) AS profit,
            IFNULL(ROUND(SUM(roi_data.ltv_total) / IFNULL(SUM(cost_data.money_cny), 0) * 100, 2), 0) AS profitRate,
            IFNULL(SUM(roi_data.ltv_1),0) AS ltv1,
            IFNULL(SUM(roi_data.ltv_2),0) AS ltv2,
            IFNULL(SUM(roi_data.ltv_3),0) AS ltv3,
            IFNULL(SUM(roi_data.ltv_4),0) AS ltv4,
            IFNULL(SUM(roi_data.ltv_5),0) AS ltv5,
            IFNULL(SUM(roi_data.ltv_6),0) AS ltv6,
            IFNULL(SUM(roi_data.ltv_7),0) AS ltv7,
            IFNULL(SUM(roi_data.ltv_15),0) AS ltv15,
            IFNULL(SUM(roi_data.ltv_30),0) AS ltv30,
            IFNULL(SUM(roi_data.ltv_45),0) AS ltv45,
            IFNULL(SUM(roi_data.ltv_60),0) AS ltv60,
            IFNULL(SUM(roi_data.ltv_90),0) AS ltv90,
            IFNULL(SUM(roi_data.ltv_120),0) AS ltv120,
            IFNULL(SUM(roi_data.ltv_150),0) AS ltv150,
            IFNULL(SUM(roi_data.ltv_180),0) AS ltv180,
            IFNULL(SUM(roi_data.ltv_210),0) AS ltv210,
            IFNULL(SUM(roi_data.ltv_240),0) AS ltv240,
            IFNULL(SUM(roi_data.ltv_270),0) AS ltv270,
            IFNULL(SUM(roi_data.ltv_300),0) AS ltv300,
            IFNULL(SUM(roi_data.ltv_330),0) AS ltv330,
            IFNULL(SUM(roi_data.ltv_360),0) AS ltv360,
            IFNULL(SUM(roi_data.ltv_total),0) AS ltvTotal
        FROM
            (
                SELECT
                    sd.date,
                    IFNULL(SUM(base_data.friend_num),0) AS friend_num,
                    IFNULL(SUM(base_data.customer_num),0) AS customer_num,
                    SUM(CASE WHEN base_data.ltv_total != 0 THEN 1 ELSE 0 END) AS recharge_num,
                    IFNULL(SUM(base_data.ltv_1),0) AS ltv_1,
                    IFNULL(SUM(base_data.ltv_2),0) AS ltv_2,
                    IFNULL(SUM(base_data.ltv_3),0) AS ltv_3,
                    IFNULL(SUM(base_data.ltv_4),0) AS ltv_4,
                    IFNULL(SUM(base_data.ltv_5),0) AS ltv_5,
                    IFNULL(SUM(base_data.ltv_6),0) AS ltv_6,
                    IFNULL(SUM(base_data.ltv_7),0) AS ltv_7,
                    IFNULL(SUM(base_data.ltv_15),0) AS ltv_15,
                    IFNULL(SUM(base_data.ltv_30),0) AS ltv_30,
                    IFNULL(SUM(base_data.ltv_45),0) AS ltv_45,
                    IFNULL(SUM(base_data.ltv_60),0) AS ltv_60,
                    IFNULL(SUM(base_data.ltv_90),0) AS ltv_90,
                    IFNULL(SUM(base_data.ltv_120),0) AS ltv_120,
                    IFNULL(SUM(base_data.ltv_150),0) AS ltv_150,
                    IFNULL(SUM(base_data.ltv_180),0) AS ltv_180,
                    IFNULL(SUM(base_data.ltv_210),0) AS ltv_210,
                    IFNULL(SUM(base_data.ltv_240),0) AS ltv_240,
                    IFNULL(SUM(base_data.ltv_270),0) AS ltv_270,
                    IFNULL(SUM(base_data.ltv_300),0) AS ltv_300,
                    IFNULL(SUM(base_data.ltv_330),0) AS ltv_330,
                    IFNULL(SUM(base_data.ltv_360),0) AS ltv_360,
                    IFNULL(SUM(base_data.ltv_total),0) AS ltv_total
                FROM
                    (
                        SELECT date FROM yooa_system.sys_date WHERE date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                    ) sd
                LEFT JOIN
                    (
                        SELECT
                            f.friend_id,
                            f.record_date,
                            COUNT(DISTINCT f.friend_id) AS friend_num,
                            COUNT(DISTINCT cf.friend_id) AS customer_num,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 0 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 0 DAY) THEN co.order_money ELSE 0 END) AS ltv_1,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 1 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 1 DAY) THEN co.order_money ELSE 0 END) AS ltv_2,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 2 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 2 DAY) THEN co.order_money ELSE 0 END) AS ltv_3,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 3 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 3 DAY) THEN co.order_money ELSE 0 END) AS ltv_4,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 4 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 4 DAY) THEN co.order_money ELSE 0 END) AS ltv_5,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 5 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 5 DAY) THEN co.order_money ELSE 0 END) AS ltv_6,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 6 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 6 DAY) THEN co.order_money ELSE 0 END) AS ltv_7,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 14 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 14 DAY) THEN co.order_money ELSE 0 END) AS ltv_15,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 29 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 29 DAY) THEN co.order_money ELSE 0 END) AS ltv_30,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 44 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 44 DAY) THEN co.order_money ELSE 0 END) AS ltv_45,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 59 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 59 DAY) THEN co.order_money ELSE 0 END) AS ltv_60,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 89 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 89 DAY) THEN co.order_money ELSE 0 END) AS ltv_90,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 119 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 119 DAY) THEN co.order_money ELSE 0 END) AS ltv_120,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 149 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 149 DAY) THEN co.order_money ELSE 0 END) AS ltv_150,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 179 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 179 DAY) THEN co.order_money ELSE 0 END) AS ltv_180,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 209 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 209 DAY) THEN co.order_money ELSE 0 END) AS ltv_210,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 239 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 239 DAY) THEN co.order_money ELSE 0 END) AS ltv_240,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 269 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 269 DAY) THEN co.order_money ELSE 0 END) AS ltv_270,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 299 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 299 DAY) THEN co.order_money ELSE 0 END) AS ltv_300,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 329 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 329 DAY) THEN co.order_money ELSE 0 END) AS ltv_330,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 359 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 359 DAY) THEN co.order_money ELSE 0 END) AS ltv_360,
                            SUM(CASE WHEN co.order_date >= f.record_date THEN co.order_money ELSE 0 END) AS ltv_total
                        FROM
                            (
                                SELECT
                                    friend_id,
                                    record_date
                                FROM crm_friend
                                <where>
                                    <if test="query.mainChannelId != null">
                                        AND main_channel_id = #{query.mainChannelId}
                                    </if>
                                    <if test="query.subChannelId != null">
                                        AND sub_channel_id = #{query.subChannelId}
                                    </if>
                                    <if test="query.language != null and query.language != ''">
                                        AND language = #{query.language}
                                    </if>
                                    <if test="query.sex != null and query.sex != ''">
                                        AND sex = #{query.sex}
                                    </if>
                                    AND (record_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]})
                                    AND extend_id IN
                                    (
                                        SELECT
                                            user_id
                                        FROM yooa_system.sys_user u
                                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                                        <where>
                                            AND d.dept_type = 1
                                            <if test="query.extendDeptId != null">
                                                AND (u.dept_id = #{query.extendDeptId} OR FIND_IN_SET(#{query.extendDeptId}, d.ancestors))
                                            </if>
                                            <if test="query.extendId != null">
                                                AND u.user_id = #{query.extendId}
                                            </if>
                                            <if test="query.extendId == null">
                                                ${query.params.dataScope}
                                            </if>
                                        </where>
                                    )
                                </where>
                            ) f
                        LEFT JOIN
                            (
                                SELECT
                                    friend_id,
                                    customer_id,
                                    py_extend_id,
                                    DATE(begin_time) AS begin_date,
                                    DATE(end_time) AS end_date
                                FROM crm_customer_friend
                            ) cf ON f.friend_id = cf.friend_id
                        LEFT JOIN
                            (
                                SELECT
                                    customer_id,
                                    py_extend_id,
                                    order_money,
                                    DATE(order_time) AS order_date
                                FROM crm_customer_order
                            ) co ON co.customer_id = cf.customer_id AND co.py_extend_id = cf.py_extend_id
                                    AND co.order_date >= cf.begin_date AND (co.order_date &lt; cf.end_date OR cf.end_date IS NULL)
                        GROUP BY
                        f.friend_id,
                        f.record_date
                    ) base_data ON sd.date = base_data.record_date
                GROUP BY sd.date
            ) roi_data
        LEFT JOIN
            (
                SELECT
                    cost_date,
                    COUNT(DISTINCT extend_id) AS pitcher_num,
                    SUM(money_cny) AS money_cny,
                    SUM(fb_click) AS fb_click,
                    SUM(tk_friend) AS tk_friend,
                    SUM(tk_effective_friend) AS tk_effective_friend
                FROM yooa_crm.crm_cost
                <where>
                    <if test="query.mainChannelId != null">
                        AND main_channel_id = #{query.mainChannelId}
                    </if>
                    <if test="query.subChannelId != null">
                        AND sub_channel_id = #{query.subChannelId}
                    </if>
                    <if test="query.language != null and query.language != ''">
                        AND language = #{query.language}
                    </if>
                    <if test="query.sex != null and query.sex != ''">
                        AND sex = #{query.sex}
                    </if>
                    AND cost_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                    AND extend_id IN
                    (
                        SELECT
                            user_id
                        FROM yooa_system.sys_user u
                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                        <where>
                            AND d.dept_type = 1
                            <if test="query.extendDeptId != null">
                                AND (u.dept_id = #{query.extendDeptId} OR FIND_IN_SET(#{query.extendDeptId}, d.ancestors))
                            </if>
                            <if test="query.extendId != null">
                                AND u.user_id = #{query.extendId}
                            </if>
                            <if test="query.extendId == null">
                                ${query.params.dataScope}
                            </if>
                        </where>
                    )
                </where>
                GROUP BY cost_date
            ) cost_data ON roi_data.date = cost_data.cost_date
        WHERE roi_data.friend_num != 0
            OR (cost_data.money_cny != 0 AND cost_data.money_cny IS NOT NULL )
            OR (cost_data.fb_click != 0 AND cost_data.fb_click IS NOT NULL )
            OR (cost_data.tk_friend != 0 AND cost_data.tk_friend IS NOT NULL )
            OR (cost_data.tk_effective_friend != 0 AND cost_data.tk_effective_friend IS NOT NULL )
    </select>

    <select id="selectExtendRoiGroupByDate" resultType="com.yooa.crm.api.domain.vo.DateRoiVo">
        SELECT
            roi_data.*,
            IFNULL(cost_data.pitcher_num, 0) AS pitcherNum,
            IFNULL(cost_data.money_cny, 0) AS moneyCny,
            IFNULL(cost_data.fb_click, 0) AS fbClick,
            IFNULL(cost_data.tk_friend, 0) AS tkFriend,
            IFNULL(cost_data.tk_effective_friend, 0) AS tkEffectiveFriend,
            IFNULL(ROUND(roi_data.rechargeNum / roi_data.customerNum * 100, 2), 0) AS rechargeRate,
            IFNULL(ROUND(roi_data.ltvTotal / roi_data.customerNum, 2), 0) AS arpu,
            IFNULL(ROUND(roi_data.ltvTotal / roi_data.rechargeNum, 2), 0) AS arppu,
            IFNULL(ROUND(roi_data.ltvTotal - IFNULL(cost_data.money_cny, 0), 2), 0) AS profit,
            IFNULL(ROUND(roi_data.ltvTotal / IFNULL(cost_data.money_cny, 0) * 100, 2), 0) AS profitRate
        FROM
            (
                SELECT
                    sd.date,
                    IFNULL(SUM(base_data.friend_num),0) AS friendNum,
                    IFNULL(SUM(base_data.customer_num),0) AS customerNum,
                    SUM(CASE WHEN base_data.ltv_total != 0 THEN 1 ELSE 0 END) AS rechargeNum,
                    IFNULL(SUM(base_data.ltv_1),0) AS ltv_1,
                    IFNULL(SUM(base_data.ltv_2),0) AS ltv_2,
                    IFNULL(SUM(base_data.ltv_3),0) AS ltv_3,
                    IFNULL(SUM(base_data.ltv_4),0) AS ltv_4,
                    IFNULL(SUM(base_data.ltv_5),0) AS ltv_5,
                    IFNULL(SUM(base_data.ltv_6),0) AS ltv_6,
                    IFNULL(SUM(base_data.ltv_7),0) AS ltv_7,
                    IFNULL(SUM(base_data.ltv_15),0) AS ltv_15,
                    IFNULL(SUM(base_data.ltv_30),0) AS ltv_30,
                    IFNULL(SUM(base_data.ltv_45),0) AS ltv_45,
                    IFNULL(SUM(base_data.ltv_60),0) AS ltv_60,
                    IFNULL(SUM(base_data.ltv_90),0) AS ltv_90,
                    IFNULL(SUM(base_data.ltv_120),0) AS ltv_120,
                    IFNULL(SUM(base_data.ltv_150),0) AS ltv_150,
                    IFNULL(SUM(base_data.ltv_180),0) AS ltv_180,
                    IFNULL(SUM(base_data.ltv_210),0) AS ltv_210,
                    IFNULL(SUM(base_data.ltv_240),0) AS ltv_240,
                    IFNULL(SUM(base_data.ltv_270),0) AS ltv_270,
                    IFNULL(SUM(base_data.ltv_300),0) AS ltv_300,
                    IFNULL(SUM(base_data.ltv_330),0) AS ltv_330,
                    IFNULL(SUM(base_data.ltv_360),0) AS ltv_360,
                    IFNULL(SUM(base_data.ltv_total),0) AS ltvTotal
                FROM
                    (
                        SELECT date FROM yooa_system.sys_date WHERE date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                    ) sd
                LEFT JOIN
                    (
                        SELECT
                            f.friend_id,
                            f.record_date,
                            COUNT(DISTINCT f.friend_id) AS friend_num,
                            COUNT(DISTINCT cf.friend_id) AS customer_num,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 0 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 0 DAY) THEN co.order_money ELSE 0 END) AS ltv_1,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 1 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 1 DAY) THEN co.order_money ELSE 0 END) AS ltv_2,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 2 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 2 DAY) THEN co.order_money ELSE 0 END) AS ltv_3,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 3 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 3 DAY) THEN co.order_money ELSE 0 END) AS ltv_4,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 4 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 4 DAY) THEN co.order_money ELSE 0 END) AS ltv_5,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 5 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 5 DAY) THEN co.order_money ELSE 0 END) AS ltv_6,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 6 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 6 DAY) THEN co.order_money ELSE 0 END) AS ltv_7,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 14 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 14 DAY) THEN co.order_money ELSE 0 END) AS ltv_15,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 29 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 29 DAY) THEN co.order_money ELSE 0 END) AS ltv_30,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 44 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 44 DAY) THEN co.order_money ELSE 0 END) AS ltv_45,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 59 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 59 DAY) THEN co.order_money ELSE 0 END) AS ltv_60,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 89 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 89 DAY) THEN co.order_money ELSE 0 END) AS ltv_90,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 119 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 119 DAY) THEN co.order_money ELSE 0 END) AS ltv_120,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 149 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 149 DAY) THEN co.order_money ELSE 0 END) AS ltv_150,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 179 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 179 DAY) THEN co.order_money ELSE 0 END) AS ltv_180,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 209 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 209 DAY) THEN co.order_money ELSE 0 END) AS ltv_210,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 239 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 239 DAY) THEN co.order_money ELSE 0 END) AS ltv_240,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 269 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 269 DAY) THEN co.order_money ELSE 0 END) AS ltv_270,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 299 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 299 DAY) THEN co.order_money ELSE 0 END) AS ltv_300,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 329 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 329 DAY) THEN co.order_money ELSE 0 END) AS ltv_330,
                            SUM(CASE WHEN DATE_ADD(f.record_date, INTERVAL 359 DAY) &lt;= SYSDATE() AND co.order_date BETWEEN
                            f.record_date AND DATE_ADD(f.record_date, INTERVAL 359 DAY) THEN co.order_money ELSE 0 END) AS ltv_360,
                            SUM(CASE WHEN co.order_date >= f.record_date THEN co.order_money ELSE 0 END) AS ltv_total
                        FROM
                            (
                                SELECT
                                    friend_id,
                                    record_date
                                FROM crm_friend
                                <where>
                                    <if test="query.mainChannelId != null">
                                        AND main_channel_id = #{query.mainChannelId}
                                    </if>
                                    <if test="query.subChannelId != null">
                                        AND sub_channel_id = #{query.subChannelId}
                                    </if>
                                    <if test="query.language != null and query.language != ''">
                                        AND language = #{query.language}
                                    </if>
                                    <if test="query.sex != null and query.sex != ''">
                                        AND sex = #{query.sex}
                                    </if>
                                    AND (record_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]})
                                    AND extend_id IN
                                    (
                                        SELECT
                                            user_id
                                        FROM yooa_system.sys_user u
                                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                                        <where>
                                            AND d.dept_type = 1
                                            <if test="query.extendDeptId != null">
                                                AND (u.dept_id = #{query.extendDeptId} OR FIND_IN_SET(#{query.extendDeptId}, d.ancestors))
                                            </if>
                                            <if test="query.extendId != null">
                                                AND u.user_id = #{query.extendId}
                                            </if>
                                            <if test="query.extendId == null">
                                                ${query.params.dataScope}
                                            </if>
                                        </where>
                                    )
                                </where>
                            ) f
                        LEFT JOIN
                            (
                                SELECT
                                    friend_id,
                                    customer_id,
                                    py_extend_id,
                                    DATE(begin_time) AS begin_date,
                                    DATE(end_time) AS end_date
                                FROM crm_customer_friend
                            ) cf ON f.friend_id = cf.friend_id
                        LEFT JOIN
                            (
                                SELECT
                                    customer_id,
                                    py_extend_id,
                                    order_money,
                                    DATE(order_time) AS order_date
                                FROM crm_customer_order
                            ) co ON co.customer_id = cf.customer_id AND co.py_extend_id = cf.py_extend_id
                                    AND co.order_date >= cf.begin_date AND (co.order_date &lt; cf.end_date OR cf.end_date IS NULL)
                        GROUP BY
                        f.friend_id,
                        f.record_date
                    ) base_data ON sd.date = base_data.record_date
                GROUP BY sd.date
            ) roi_data
        LEFT JOIN
            (
                SELECT
                    cost_date,
                    COUNT(DISTINCT extend_id) AS pitcher_num,
                    SUM(money_cny) AS money_cny,
                    SUM(fb_click) AS fb_click,
                    SUM(tk_friend) AS tk_friend,
                    SUM(tk_effective_friend) AS tk_effective_friend
                FROM yooa_crm.crm_cost
                <where>
                    <if test="query.mainChannelId != null">
                        AND main_channel_id = #{query.mainChannelId}
                    </if>
                    <if test="query.subChannelId != null">
                        AND sub_channel_id = #{query.subChannelId}
                    </if>
                    <if test="query.language != null and query.language != ''">
                        AND language = #{query.language}
                    </if>
                    <if test="query.sex != null and query.sex != ''">
                        AND sex = #{query.sex}
                    </if>
                    AND cost_date BETWEEN #{query.dateRange[0]} AND #{query.dateRange[1]}
                    AND extend_id IN
                    (
                        SELECT
                            user_id
                        FROM yooa_system.sys_user u
                        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                        <where>
                            AND d.dept_type = 1
                            <if test="query.extendDeptId != null">
                                AND (u.dept_id = #{query.extendDeptId} OR FIND_IN_SET(#{query.extendDeptId}, d.ancestors))
                            </if>
                            <if test="query.extendId != null">
                                AND u.user_id = #{query.extendId}
                            </if>
                            <if test="query.extendId == null">
                                ${query.params.dataScope}
                            </if>
                        </where>
                    )
                </where>
                GROUP BY cost_date
            ) cost_data ON roi_data.date = cost_data.cost_date
        WHERE roi_data.friendNum != 0
            OR (cost_data.money_cny != 0 AND cost_data.money_cny IS NOT NULL )
            OR (cost_data.fb_click != 0 AND cost_data.fb_click IS NOT NULL )
            OR (cost_data.tk_friend != 0 AND cost_data.tk_friend IS NOT NULL )
            OR (cost_data.tk_effective_friend != 0 AND cost_data.tk_effective_friend IS NOT NULL )
    </select>

</mapper>
