<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.yooa</groupId>
        <artifactId>yooa-common</artifactId>
        <version>3.6.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yooa-common-security</artifactId>

    <description>
        yooa-common-security安全模块
    </description>

    <dependencies>

        <!-- Spring Web -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>

        <!-- YoOA Api System -->
        <dependency>
            <groupId>com.yooa</groupId>
            <artifactId>yooa-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yooa</groupId>
            <artifactId>yooa-api-cmf</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yooa</groupId>
            <artifactId>yooa-api-crm</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yooa</groupId>
            <artifactId>yooa-api-antflow</artifactId>
            <version>3.6.4</version>
        </dependency>

        <dependency>
            <groupId>com.yooa</groupId>
            <artifactId>yooa-api-extend</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yooa</groupId>
            <artifactId>yooa-api-external</artifactId>
        </dependency>

        <!-- YoOA Common Redis-->
        <dependency>
            <groupId>com.yooa</groupId>
            <artifactId>yooa-common-redis</artifactId>
        </dependency>

    </dependencies>

</project>
