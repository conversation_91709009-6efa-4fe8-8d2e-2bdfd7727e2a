<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yooa</groupId>
        <artifactId>yooa-common</artifactId>
        <version>3.6.4</version>
    </parent>

    <artifactId>yooa-common-kafka</artifactId>


<!--    <dependencies>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.kafka</groupId>-->
<!--            <artifactId>kafka-clients</artifactId>-->
<!--            <version>2.0.0</version>-->
<!--        </dependency>-->
<!--    </dependencies>-->

    <dependencies>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
    </dependencies>

<!--        <dependencies>-->
<!--            <dependency>-->
<!--                <groupId>org.springframework.cloud</groupId>-->
<!--                <artifactId>spring-cloud-starter-stream-kafka</artifactId>-->
<!--            </dependency>-->
<!--        </dependencies>-->
</project>